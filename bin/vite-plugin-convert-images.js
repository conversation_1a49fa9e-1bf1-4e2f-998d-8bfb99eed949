import path from 'path';
import sharp from 'sharp';
import { watch } from 'chokidar';
import { promises as fs } from 'fs';
export default function convertImages(options = {
    format: 'webp',
    compressOriginal: true,
    devMode: false
}) {
    // デフォルトはwebp形式に変換＋元画像も圧縮
    // devMode: trueの場合、新規追加時のみ処理を実行

    // 圧縮済みかどうかをチェックする関数
    async function isAlreadyProcessed(filePath) {
        try {
            const stats = await fs.stat(filePath);
            const dir = path.dirname(filePath);
            const base = path.basename(filePath, path.extname(filePath));
            const webpPath = path.resolve(dir, `${base}.webp`);

            // WebPファイルが存在し、元画像より新しい場合は処理済みとみなす
            try {
                const webpStats = await fs.stat(webpPath);
                return webpStats.mtime >= stats.mtime;
            } catch {
                return false; // WebPファイルが存在しない場合は未処理
            }
        } catch {
            return false;
        }
    }
    return {
        name: 'convert-images', // プラグイン名
        enforce: 'pre',
        buildStart() {
            if (options.devMode) {
                console.log('Image conversion plugin: Development mode - only processing new images');
            } else {
                console.log('Image conversion plugin: Build mode - processing all images');
            }
            const watcher = watch('src/public/images/**/*.{png,jpg,jpeg}', {
                persistent: true
            });
            watcher.on('add', async (filePath) => {
                if (/\.(png|jpe?g)$/.test(filePath)) {
                    console.log(`New image detected: ${path.basename(filePath)}`);

                    // 既に処理済みかチェック
                    if (await isAlreadyProcessed(filePath)) {
                        console.log(`Already processed: ${path.basename(filePath)}`);
                        return;
                    }

                    const dir = path.dirname(filePath);
                    const base = path.basename(filePath, path.extname(filePath));
                    const ext = path.extname(filePath).toLowerCase();
                    const avifPath = path.resolve(dir, `${base}.avif`);
                    const webpPath = path.resolve(dir, `${base}.webp`);

                    // 元画像の圧縮処理
                    if (options.compressOriginal) {
                        console.log(`Compressing original: ${path.basename(filePath)}`);
                        try {
                            if (ext === '.jpg' || ext === '.jpeg') {
                                await sharp(filePath)
                                    .jpeg({
                                        quality: 80,
                                        progressive: true
                                    })
                                    .toFile(filePath + '.tmp');
                            } else if (ext === '.png') {
                                await sharp(filePath)
                                    .png({
                                        quality: 80,
                                        compressionLevel: 9
                                    })
                                    .toFile(filePath + '.tmp');
                            }
                            // 元ファイルを圧縮版で置き換え
                            const fs = await import('fs');
                            await fs.promises.rename(filePath + '.tmp', filePath);
                            console.log(`Compressed: ${path.basename(filePath)}`);
                        } catch (err) {
                            console.error('Compression error:', err);
                        }
                    }

                    // WebP/AVIF変換処理
                    if (options.format === 'webp') {
                        console.log(`Converting to WebP: ${path.basename(filePath)}`);
                        await sharp(filePath)
                            .webp({
                                quality: 80
                            })
                            .toFile(webpPath)
                            .then(() => console.log(`WebP created: ${path.basename(webpPath)}`))
                            .catch(err => console.error('WEBP conversion error:', err));
                    } else if (options.format === 'avif') {
                        console.log(`Converting to AVIF: ${path.basename(filePath)}`);
                        await sharp(filePath)
                            .avif({
                                quality: 80
                            })
                            .toFile(avifPath)
                            .then(() => console.log(`AVIF created: ${path.basename(avifPath)}`))
                            .catch(err => console.error('AVIF conversion error:', err));
                    }
                }
            });
        },
        async transform(src, id) {
            // 開発モードでは transform での処理をスキップ（新規追加時のみ処理）
            if (options.devMode) {
                return null;
            }

            if (/\.(png|jpe?g)$/.test(id)) {
                // 既に処理済みかチェック
                if (await isAlreadyProcessed(id)) {
                    console.log(`Already processed: ${path.basename(id)}`);
                    return null;
                }

                const dir = path.dirname(id);
                const base = path.basename(id, path.extname(id));
                const ext = path.extname(id).toLowerCase();
                const avifPath = path.resolve(dir, `${base}.avif`);
                const webpPath = path.resolve(dir, `${base}.webp`);

                // 元画像の圧縮処理
                if (options.compressOriginal) {
                    console.log(`Compressing original: ${path.basename(id)}`);
                    try {
                        if (ext === '.jpg' || ext === '.jpeg') {
                            await sharp(id)
                                .jpeg({
                                    quality: 80,
                                    progressive: true
                                })
                                .toFile(id + '.tmp');
                        } else if (ext === '.png') {
                            await sharp(id)
                                .png({
                                    quality: 80,
                                    compressionLevel: 9
                                })
                                .toFile(id + '.tmp');
                        }

                        // 元ファイルを圧縮版で置き換え
                        const fs = await import('fs');
                        await fs.promises.rename(id + '.tmp', id);
                        console.log(`Compressed: ${path.basename(id)}`);
                    } catch (err) {
                        console.error('Compression error:', err);
                    }
                }

                // WebP/AVIF変換処理
                if (options.format === 'webp') {
                    console.log(`Converting to WebP: ${path.basename(id)}`);
                    await sharp(id)
                        .webp({
                            quality: 80
                        })
                        .toFile(webpPath)
                        .then(() => console.log(`WebP created: ${path.basename(webpPath)}`))
                        .catch(err => console.error('WEBP conversion error:', err));
                } else if (options.format === 'avif') {
                    console.log(`Converting to AVIF: ${path.basename(id)}`);
                    await sharp(id)
                        .avif({
                            quality: 80
                        })
                        .toFile(avifPath)
                        .then(() => console.log(`AVIF created: ${path.basename(avifPath)}`))
                        .catch(err => console.error('AVIF conversion error:', err));
                }
                return null; // 元の画像ファイルの処理は行わない
            }
        },
    };
}