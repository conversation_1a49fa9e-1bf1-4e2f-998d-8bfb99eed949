/**
 * 郵便番号フォームのバリデーションとエラーハンドリング
 * Contact Form 7と連携した郵便番号入力フィールドの検証を行う
 */
document.addEventListener("DOMContentLoaded", function() {
  
  /**
   * 定数とメッセージの定義
   */
  const CONSTANTS = {
    // セレクタ
    SELECTORS: {
      ZIP_FIELD: 'input[name="your-zip"]',
      ERROR_MESSAGE: '.p-contact__error-message',
      ZIP_WRAP: '.p-contact__zip-wrap',
      ZIP_BUTTON: '.p-contact__zip-button',
      CF7_WRAP: '.wpcf7-form-control-wrap',
      CF7_ERROR_TIP: '.wpcf7-not-valid-tip',
      ADDRESS_FIELD: '.p-region'
    },
    // CSSクラス
    CSS_CLASSES: {
      ERROR_MESSAGE: 'p-contact__error-message',
      NOT_VALID: 'wpcf7-not-valid'
    },
    // タイミング
    TIMING: {
      CF7_HIDE_DELAY: 10,
      ADDRESS_CHECK_DELAY: 300
    }
  };

  /**
   * エラーメッセージの定義
   */
  const ERROR_MESSAGES = {
    REQUIRED: '※入力してください。',
    INVALID_FORMAT: '※半角数字で入力してください。',
    ADDRESS_NOT_FOUND: '※該当する住所が見つかりません。'
  };

  /**
   * DOM操作を行うヘルパークラス
   * UI要素の取得と基本操作を担当
   */
  class DOMHelper {
    /**
     * 郵便番号入力フィールドを取得
     * @returns {HTMLInputElement|null} 郵便番号入力フィールド
     */
    static getZipField() {
      return document.querySelector(CONSTANTS.SELECTORS.ZIP_FIELD);
    }

    /**
     * エラーメッセージ要素を取得または作成
     * 存在しない場合は新しく作成してDOMに追加
     * @param {HTMLElement} zipWrap 郵便番号ラッパー要素
     * @returns {HTMLElement} エラーメッセージ要素
     */
    static getOrCreateErrorElement(zipWrap) {
      // 既存のエラーメッセージ要素を検索
      let errorMsg = zipWrap.querySelector(CONSTANTS.SELECTORS.ERROR_MESSAGE);
      
      // 存在しない場合は新しく作成
      if (!errorMsg) {
        errorMsg = document.createElement('div');
        errorMsg.className = CONSTANTS.CSS_CLASSES.ERROR_MESSAGE;
        errorMsg.style.display = 'none';
        zipWrap.appendChild(errorMsg);
      }
      
      return errorMsg;
    }

    /**
     * Contact Form 7のラッパー要素を取得
     * @param {HTMLElement} zipField 郵便番号入力フィールド
     * @returns {HTMLElement|null} CF7のラッパー要素
     */
    static getCF7Wrapper(zipField) {
      return zipField.closest(CONSTANTS.SELECTORS.CF7_WRAP);
    }

    /**
     * Contact Form 7のエラーチップ要素を取得
     * @param {HTMLElement} cf7Wrap CF7のラッパー要素
     * @returns {HTMLElement|null} エラーチップ要素
     */
    static getCF7ErrorTip(cf7Wrap) {
      return cf7Wrap ? cf7Wrap.querySelector(CONSTANTS.SELECTORS.CF7_ERROR_TIP) : null;
    }

    /**
     * 住所フィールドを取得
     * @returns {HTMLInputElement|null} 住所フィールド
     */
    static getAddressField() {
      return document.querySelector(CONSTANTS.SELECTORS.ADDRESS_FIELD);
    }

    /**
     * 全ての郵便番号ラッパー要素を取得
     * @returns {NodeList} 郵便番号ラッパー要素のリスト
     */
    static getAllZipWraps() {
      return document.querySelectorAll(CONSTANTS.SELECTORS.ZIP_WRAP);
    }

    /**
     * 全ての郵便番号ボタンを取得
     * @returns {NodeList} 郵便番号ボタンのリスト
     */
    static getAllZipButtons() {
      return document.querySelectorAll(CONSTANTS.SELECTORS.ZIP_BUTTON);
    }
  }

  /**
   * エラーハンドリングを行うクラス
   * エラーメッセージの表示/非表示とフィールドの状態管理を担当
   */
  class ErrorHandler {
    /**
     * Contact Form 7のデフォルトエラーメッセージを非表示にする
     * 赤枠は残してメッセージのみを隠す
     * @param {HTMLElement} zipField 郵便番号入力フィールド
     */
    static hideDefaultErrorMessage(zipField) {
      const cf7Wrap = DOMHelper.getCF7Wrapper(zipField);
      const errorTip = DOMHelper.getCF7ErrorTip(cf7Wrap);
      
      if (errorTip) {
        errorTip.style.display = 'none';
      }
    }

    /**
     * エラー状態をクリア
     * 赤枠とエラーメッセージを両方とも削除
     * @param {HTMLElement} zipField 郵便番号入力フィールド
     */
    static clearErrorState(zipField) {
      // CF7のデフォルトエラーメッセージを非表示
      this.hideDefaultErrorMessage(zipField);
      
      // 赤枠のクラスを削除
      zipField.classList.remove(CONSTANTS.CSS_CLASSES.NOT_VALID);
    }

    /**
     * エラー状態を設定
     * 赤枠を表示してデフォルトエラーメッセージを非表示にする
     * @param {HTMLElement} zipField 郵便番号入力フィールド
     */
    static setErrorState(zipField) {
      // 赤枠のクラスを追加
      zipField.classList.add(CONSTANTS.CSS_CLASSES.NOT_VALID);
      
      // CF7のデフォルトエラーメッセージは非表示のまま
      this.hideDefaultErrorMessage(zipField);
    }

    /**
     * エラーメッセージを表示
     * カスタムエラーメッセージを表示し、フィールドにエラー状態を設定
     * @param {HTMLElement} errorElement エラーメッセージ要素
     * @param {string} message 表示するメッセージ
     * @param {HTMLElement} zipField 郵便番号入力フィールド
     */
    static showError(errorElement, message, zipField) {
      // エラーメッセージのテキストを設定
      errorElement.textContent = message;
      errorElement.style.display = 'block';
      
      // フィールドにエラー状態を設定
      this.setErrorState(zipField);
    }

    /**
     * エラーメッセージを非表示
     * カスタムエラーメッセージを隠し、フィールドのエラー状態をクリア
     * @param {HTMLElement} errorElement エラーメッセージ要素
     * @param {HTMLElement} zipField 郵便番号入力フィールド
     */
    static hideError(errorElement, zipField) {
      // エラーメッセージを非表示
      errorElement.style.display = 'none';
      
      // フィールドのエラー状態をクリア
      if (zipField) {
        this.clearErrorState(zipField);
      }
    }
  }

  /**
   * 郵便番号バリデーションを行うクラス
   * 郵便番号の形式チェックと住所検索結果の確認を担当
   */
  class ZipCodeValidator {
    /**
     * 郵便番号の形式をチェック
     * 数字とハイフンのみを許可
     * @param {string} zipCode 検証する郵便番号
     * @returns {boolean} 有効な形式の場合true
     */
    static isValidFormat(zipCode) {
      return /^[0-9-]+$/.test(zipCode.trim());
    }

    /**
     * 住所自動入力の結果をチェック
     * 住所フィールドに値が入力されているかを確認
     * @returns {boolean} 住所が見つかった場合true
     */
    static checkAddressFound() {
      const addressField = DOMHelper.getAddressField();
      return addressField && addressField.value;
    }

    /**
     * 郵便番号の値をバリデーション
     * @param {string} zipCode 検証する郵便番号
     * @returns {string|null} エラーメッセージ、問題なければnull
     */
    static validateZipCode(zipCode) {
      // 空文字チェック
      if (!zipCode.trim()) {
        return ERROR_MESSAGES.REQUIRED;
      }
      
      // 形式チェック
      if (!this.isValidFormat(zipCode)) {
        return ERROR_MESSAGES.INVALID_FORMAT;
      }
      
      return null;
    }
  }

  /**
   * イベントハンドラーを管理するクラス
   * 各種イベントリスナーの設定とイベント処理を担当
   */
  class EventManager {
    /**
     * Contact Form 7の無効イベントを処理
     * フォーム送信時のバリデーションエラーに対応
     * @param {Event} event CF7の無効イベント
     */
    static handleCF7Invalid(event) {
      const zipField = DOMHelper.getZipField();
      if (!zipField) return;

      // 無効フィールドの情報を取得
      const invalidFields = event.detail.apiResponse.invalid_fields;
      if (!invalidFields['your-zip']) return;

      // エラーメッセージ要素を取得
      const zipWrap = zipField.closest(CONSTANTS.SELECTORS.ZIP_WRAP);
      if (!zipWrap) return;

      const errorMsg = DOMHelper.getOrCreateErrorElement(zipWrap);
      
      // CF7のデフォルトエラーメッセージを非表示にする
      // 少し遅延させてCF7の処理後に実行
      setTimeout(() => {
        ErrorHandler.hideDefaultErrorMessage(zipField);
      }, CONSTANTS.TIMING.CF7_HIDE_DELAY);

      // エラーメッセージを判定して表示
      const errorMessage = ZipCodeValidator.validateZipCode(zipField.value);
      if (errorMessage) {
        ErrorHandler.showError(errorMsg, errorMessage, zipField);
      }
    }

    /**
     * フォーカスイベントを処理
     * 郵便番号フィールドにフォーカスした時にエラーを隠す
     * @param {Event} event フォーカスイベント
     */
    static handleFocusIn(event) {
      // 郵便番号フィールドのフォーカス時のみ処理
      if (event.target.name !== 'your-zip') return;
      
      const zipWrap = event.target.closest(CONSTANTS.SELECTORS.ZIP_WRAP);
      if (!zipWrap) return;

      // エラーメッセージを非表示
      const errorMsg = DOMHelper.getOrCreateErrorElement(zipWrap);
      ErrorHandler.hideError(errorMsg, event.target);
    }

    /**
     * 郵便番号ボタンのクリックイベントを処理
     * 郵便番号の検証と住所検索を実行
     * @param {Event} event クリックイベント
     */
    static handleZipButtonClick(event) {
      const button = event.target;
      const zipWrap = button.closest(CONSTANTS.SELECTORS.ZIP_WRAP);
      const zipField = zipWrap.querySelector(CONSTANTS.SELECTORS.ZIP_FIELD);
      
      // 必要な要素が存在しない場合は処理を中断
      if (!zipField || !zipWrap) return;

      const errorMsg = DOMHelper.getOrCreateErrorElement(zipWrap);
      
      // 郵便番号をバリデーション
      const errorMessage = ZipCodeValidator.validateZipCode(zipField.value);
      if (errorMessage) {
        ErrorHandler.showError(errorMsg, errorMessage, zipField);
        zipField.focus();
        return;
      }

      // バリデーション成功時はエラーを非表示
      ErrorHandler.hideError(errorMsg, zipField);
      
      // ボタンを無効化してローディング状態を表示
      button.disabled = true;
      button.style.opacity = '0.5';
      
      // 住所検索を実行（keyupイベントを発火）
      const keyupEvent = new Event('keyup');
      zipField.dispatchEvent(keyupEvent);
      
      // 住所検索結果を確認
      setTimeout(() => {
        if (ZipCodeValidator.checkAddressFound()) {
          // 住所が見つかった場合は住所フィールドにフォーカス
          const addressField = DOMHelper.getAddressField();
          if (addressField) {
            addressField.focus();
          }
        } else {
          // 住所が見つからない場合はエラーメッセージを表示
          ErrorHandler.showError(errorMsg, ERROR_MESSAGES.ADDRESS_NOT_FOUND, zipField);
          zipField.focus();
        }
        
        // ボタンを有効化
        button.disabled = false;
        button.style.opacity = '1';
      }, CONSTANTS.TIMING.ADDRESS_CHECK_DELAY);
    }
  }

  /**
   * 初期化処理
   * 各種イベントリスナーの設定と初期状態の構築を行う
   */
  function initialize() {
    // 全ての郵便番号ラッパーにエラーメッセージ要素を準備
    const zipWraps = DOMHelper.getAllZipWraps();
    zipWraps.forEach((zipWrap) => {
      DOMHelper.getOrCreateErrorElement(zipWrap);
    });

    // Contact Form 7の無効イベントリスナーを設定
    document.addEventListener('wpcf7invalid', EventManager.handleCF7Invalid);

    // フォーカスイベントリスナーを設定
    document.addEventListener('focusin', EventManager.handleFocusIn);

    // 郵便番号ボタンのクリックイベントリスナーを設定
    const zipButtons = DOMHelper.getAllZipButtons();
    zipButtons.forEach((button) => {
      button.addEventListener('click', EventManager.handleZipButtonClick);
    });
  }

  // 初期化を実行
  initialize();
});
