import '@splidejs/splide/css';
import Splide from '@splidejs/splide';

// Splideの利用可能性をチェック（CDNまたはローカル）
const SplideClass = window.Splide || Splide;

// カスタム自動再生用の関数（プログラムルートに移動）
function createAutoplayController(slider, greetingSlider) {
    let currentIndex = 0;
    let autoplayInterval;
    let isPlaying = true;

    const startAutoplay = () => {
        if (autoplayInterval) clearInterval(autoplayInterval);

        autoplayInterval = setInterval(() => {
            if (!isPlaying) return;

            // 次のスライドのインデックスを計算（0と1を無限に繰り返し）
            currentIndex = currentIndex === 0 ? 1 : 0;

            // スライドを移動
            slider.go(currentIndex);
        }, 4000);
    };

    const stopAutoplay = () => {
        if (autoplayInterval) {
            clearInterval(autoplayInterval);
            autoplayInterval = null;
        }
    };

    // ホバー時の制御
    greetingSlider.addEventListener('mouseenter', () => {
        isPlaying = false;
        stopAutoplay();
    });

    greetingSlider.addEventListener('mouseleave', () => {
        isPlaying = true;
        startAutoplay();
    });

    // スライドが移動した時のインデックス更新
    slider.on('moved', function(newIndex) {
        currentIndex = newIndex;
    });

    return {
        startAutoplay,
        stopAutoplay
    };
}

// Greetingスライダーの初期化
const greetingSlider = document.querySelector('.js-slider-greeting');

if (greetingSlider) {
    const slider = new SplideClass('.js-slider-greeting', {
        type: 'fade',
        perMove: 1,
        pagination: true,
        arrows: false,
        drag: true,
        loop: false, // 無限ループを無効
        rewind: false, // rewindも無効
        autoplay: false, // 自動再生はJavaScriptで制御
        interval: 4000,
        speed: 1200, // フェード時間を1.5秒に延長（より緩やかに）
        easing: 'ease-in-out', // イージング追加
        pauseOnHover: true,
        pauseOnFocus: false,
        resetProgress: false,
        lazyLoad: false,
    });

    // スライダーの起動
    slider.mount();

    // カスタム自動再生コントローラーを作成して開始
    const autoplayController = createAutoplayController(slider, greetingSlider);
    autoplayController.startAutoplay();

    console.log('Greeting slider: カスタム無限ループを開始');
}