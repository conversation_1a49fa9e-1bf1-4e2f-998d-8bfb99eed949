/* ヘッダースクロール処理 */

! function() {
    const header = document.querySelector('.js-header');
    let isScrolled = false;

    // スクロール閾値（ピクセル）
    const scrollThreshold = 60;

    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > scrollThreshold && !isScrolled) {
            // スクロールダウン：背景色を表示
            header.classList.add('is-scrolled');
            isScrolled = true;
        } else if (scrollTop <= scrollThreshold && isScrolled) {
            // スクロールアップ：背景色を非表示
            header.classList.remove('is-scrolled');
            isScrolled = false;
        }
    }

    // スクロールイベントの登録（パフォーマンス最適化のためthrottling）
    let ticking = false;

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(handleScroll);
            ticking = true;
        }
    }

    function onScroll() {
        ticking = false;
        requestTick();
    }

    // イベントリスナーの登録
    window.addEventListener('scroll', onScroll, {
        passive: true
    });

    // 初期化時の処理
    handleScroll();
}();