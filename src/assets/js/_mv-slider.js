import '@splidejs/splide/css';
import Splide from '@splidejs/splide';

// Splideの利用可能性をチェック（CDNまたはローカル）
const SplideClass = window.Splide || Splide;

// 要素の存在チェック
const mvSliderElement = document.querySelector('.js-slider-mv');
if (!mvSliderElement) {
    // 要素が存在しない場合は処理を終了
} else {
    // MVスライダーの初期化
    const slides = document.querySelectorAll('.js-slider-mv .splide__slide');
    const totalSlides = slides.length;
    let currentIndex = 0;

    const mvSlider = new SplideClass('.js-slider-mv', {
        type: 'fade',
        perMove: 1,
        pagination: false,
        arrows: false,
        drag: false,
        loop: false,
        rewind: true,
        autoplay: false,
        speed: 1800,
        pauseOnHover: false,
        pauseOnFocus: false,
        resetProgress: false,
        lazyLoad: false,
        easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    });

    // スライダーの起動
    mvSlider.mount();

    // 自動スライド関数（ES6のアロー関数で書く）
    const startAutoSlide = () => {
        const loop = () => {
            currentIndex = (currentIndex + 1) % totalSlides;
            mvSlider.go(currentIndex);
            setTimeout(loop, 5000);
        };

        setTimeout(loop, 5000);
    };

    // 自動スライド開始
    startAutoSlide();
}