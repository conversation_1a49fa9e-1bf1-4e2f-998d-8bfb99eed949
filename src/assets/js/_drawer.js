jQuery(function($) {
    // この中に記載_WordPressでも使えるように
    // ハンバーガーメニュー
    $(function() {
        $(".js-hamburger").click(function() {
            $(this).toggleClass("is-open");
            $(".js-drawer").fadeToggle();

            // 背景固定の制御
            if ($(this).hasClass("is-open")) {
                // ドロワー開いた時：背景を固定
                $("body").addClass("is-drawer-open");
                $(".js-header").addClass("is-drawer-expanded");
            } else {
                // ドロワー閉じた時：背景固定を解除
                $("body").removeClass("is-drawer-open");
                $(".js-header").removeClass("is-drawer-expanded");
            }
        });

        // ドロワーナビのaタグをクリックで閉じる（アコーディオンボタンは除外）
        $(".js-drawer a[href]:not(.js-drawer-accordion)").on("click", function() {
            $(".js-hamburger").removeClass("is-open");
            $(".js-drawer").fadeOut();
            // 背景固定を解除
            $("body").removeClass("is-drawer-open");
            $(".js-header").removeClass("is-drawer-expanded");
        });

        // Escキーでドロワーを閉じる
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $(".js-hamburger").hasClass("is-open")) {
                $(".js-hamburger").removeClass("is-open");
                $(".js-drawer").fadeOut();
                // 背景固定を解除
                $("body").removeClass("is-drawer-open");
                $(".js-header").removeClass("is-drawer-expanded");
                // フォーカスをハンバーガーボタンに戻す
                $(".js-hamburger").focus();
            }
        });

        // resizeイベント
        $(window).on('resize', function() {
            if (window.matchMedia("(min-width: 768px)").matches) {
                $(".js-hamburger").removeClass("is-open");
                $(".js-drawer").fadeOut();
                // 背景固定を解除
                $("body").removeClass("is-drawer-open");
                $(".js-header").removeClass("is-drawer-expanded");
            }
        });
        // アコーディオン
        $('.js-drawer-accordion').on('click', function() {
            let $this = $(this);
            let $submenu = $this.next();
            let isExpanded = $this.attr('aria-expanded') === 'true';

            // アコーディオンの開閉
            $submenu.slideToggle();
            $this.toggleClass('is-open');

            // aria-expanded属性を更新
            $this.attr('aria-expanded', !isExpanded);
        });
    });
});