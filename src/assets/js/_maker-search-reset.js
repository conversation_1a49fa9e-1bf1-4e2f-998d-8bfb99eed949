// メーカー検索ページ用：リロード時にURLパラメータを消して再読み込み

document.addEventListener('DOMContentLoaded', function() {
    // ページリロードを検知
    if (performance.navigation.type === 1) { // TYPE_RELOAD
        // URLにパラメータがある場合、パラメータを削除してページを再読み込み
        if (window.location.search) {
            const url = new URL(window.location);
            url.search = '';
            window.history.replaceState({}, '', url.toString());
            window.location.reload();
        }
    }
});
