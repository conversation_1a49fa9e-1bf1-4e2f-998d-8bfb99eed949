@use "../globals/"as *;

.p-mv {
    position: relative;
    z-index: 1;
    min-height: rem(482);
    height: 80vh;

    @include mq(md) {
        height: 100dvh;
    }

    // アドミンバーがある場合の高さ調整
    body.admin-bar & {
        height: calc(80vh - 46px);

        @include mq(md) {
            height: calc(100dvh - 32px);
        }
    }
}


.p-mv__inner {
    height: inherit;
    min-height: inherit;
}

.p-mv__title-wrap {
    position: absolute;
    z-index: 2;
    bottom: rem(32);
    left: 44%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;
    width: min(100%, rem(290));

    @include mq("md") {
        bottom: 0;
        left: 28.5%;
        width: min(100%, rem(640));
    }
}

.p-mv__title-wrap img {
    width: 100%;
    height: auto;
    object-fit: contain;
}

.p-mv__splide,
.p-mv__splide .splide__track,
.p-mv__splide .splide__list,
.p-mv__splide .splide__slide,
.p-mv__splide .splide__slide-image,
.p-mv__splide .splide__slide-image img {
    height: calc(80vh - rem(84));
    min-height: rem(400);

    @include mq(md) {
        height: calc(100dvh - rem(64));
    }

    // アドミンバーがある場合の高さ調整
    body.admin-bar & {
        height: calc(80vh - rem(84) - 46px);

        @include mq(md) {
            height: calc(100dvh - rem(64) - 32px);
        }
    }
}

.p-mv__splide .splide__slide-image {
    position: relative;
    overflow: hidden;
    height: 100%;
}

.p-mv__splide .splide__slide-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 8s ease-out;
    transform: scale(1);
}

// アクティブなスライドにズームアップエフェクトを適用
.p-mv__splide .splide__slide.is-active .splide__slide-image img {
    transform: scale(1.1);
}

// フェードアニメーション用のスタイル
.p-mv__splide .splide__slide {
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.p-mv__splide .splide__slide.is-active {
    opacity: 1;
}

.p-mv__banner {
    background-color: var(--color-blue-navy);
    padding: 0 var(--padding-sp);
    height: rem(84);
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid var(--color-white);

    @include mq(md) {
        padding: rem(15) rem(30);
        height: rem(64);
    }
}

// ニュースティッカーのアニメーション定義

@keyframes newsTickerRotate {
    0% {
        opacity: 0;
        transform: translateY(100%);
    }

    8.33% {
        opacity: 1;
        transform: translateY(0);
    }

    25% {
        opacity: 1;
        transform: translateY(0);
    }

    33.33% {
        opacity: 0;
        transform: translateY(-100%);
    }

    100% {
        opacity: 0;
        transform: translateY(-100%);
    }
}

.p-mv__news-ticker {
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    width: min(100%, rem(500));
    margin-inline: auto;

    @include mq("md") {
        width: min(100%, rem(800));
    }
}

.p-mv__news-item {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(100%);
    animation: newsTickerRotate 9s infinite;
}

.p-mv__news-item--1 {
    animation-delay: 0s;
}

.p-mv__news-item--2 {
    animation-delay: 3s;
}

.p-mv__news-item--3 {
    animation-delay: 6s;
}

.p-mv__news-link {
    display: flex;
    flex-direction: column;
    gap: rem(4);
    text-decoration: none;
    width: 100%;
    transition: opacity 0.3s ease;

    @include mq("md") {
        flex-direction: row;
        align-items: center;
    }

}

.p-mv__news-link:hover {
    opacity: 0.7;
}

.p-mv__news-date-wrap {
    display: flex;
    align-items: center;
    gap: rem(10);

    @include mq(md) {
        margin-right: rem(20);
    }
}


.p-mv__news-label {
    padding: rem(3) rem(12);
    background-color: var(--color-white);
    color: var(--color-gray-medium);
    font-size: rem(12);
    font-weight: var(--medium);
    letter-spacing: 0;
    line-height: calc(16 / 12);
    border-radius: rem(2);
}

.p-mv__news-date {
    font-size: rem(16);
    font-family: var(--noto-serif-jp);
    font-weight: var(--regular);
    color: var(--color-white);
    letter-spacing: 0.04em;
    line-height: 1.5;
}

.p-mv__news-text-wrap {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;

    @include mq("md") {
        position: relative;
    }
}

.p-mv__news-text-wrap::after {
    @include mq("md") {
        content: "";
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        display: inline-block;
        align-items: center;
        justify-content: center;
        margin-left: rem(10);
        width: rem(10);
        height: rem(10);
        clip-path: $clip-triangle-right;
        background-color: var(--color-white);
    }
}

.p-mv__news-text {
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-white);
    letter-spacing: 0.04em;
    line-height: calc(24 / 16);
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;

    @include mq(md) {
        padding-right: rem(20);
    }
}