@use "../globals/"as *;

.p-thanks {
    padding-block: rem(60) rem(82);

    @include mq("md") {
        padding-block: rem(100) rem(100);
    }
}


.p-thanks__title {
    position: relative;
    font-size: rem(22);
    font-weight: var(--bold);
    line-height: calc(33 / 22);
    letter-spacing: 0;
    color: var(--color-dark-gray);
    text-align: center;
    padding-bottom: rem(35);

    @include mq("md") {
        font-size: rem(34);
        line-height: calc(46 / 34);
        padding-bottom: rem(27);
        letter-spacing: 0.04em;
    }
}



.p-thanks__title::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: rem(50);
    height: rem(4);
    background-color: var(--color-orange-dark);

    @include mq("md") {
        width: rem(72);
    }
}

.p-thanks__text-wrap {
    text-align: center;
    margin-top: rem(27);
    display: grid;
    gap: rem(10);

    @include mq("md") {
        margin-top: rem(37);
        gap: rem(13);
    }
}

.p-thanks__text {
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;
    color: var(--color-dark-gray);
    text-align: center;
}

.p-thanks__text:nth-child(3) {
    margin-top: rem(-10);

    @include mq("md") {}
}

.p-thanks__button-wrap {
    margin-top: rem(27);
    max-width: rem(240);
    margin-inline: auto;

    @include mq("md") {
        margin-top: rem(53);
        max-width: rem(270);
    }
}