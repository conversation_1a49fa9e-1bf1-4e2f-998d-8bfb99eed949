@use "../globals/"as *;

.p-sub-mv {
    background-color: var(--color-blue-navy);
    padding-top: rem(60);
    overflow-x: clip;

    @include mq(md) {
        padding-top: rem(100);
    }
}

.p-sub-mv.p-sub-mv--common {
    padding-block: rem(60) rem(40);

    @include mq(md) {
        padding-block: rem(100) rem(49);
    }
}

.p-sub-mv.p-sub-mv--maker {
    padding-block: rem(60) rem(29);

    @include mq(md) {
        padding-block: rem(100) rem(36);
    }
}

.p-sub-mv.p-sub-mv--privacypolicy {
    padding-block: rem(60) rem(24);

    @include mq(md) {
        padding-block: rem(100) rem(50);
    }
}

.p-sub-mv__inner {
    margin-top: rem(19);

    @include mq(md) {
        margin-top: rem(66);
        margin-inline: auto;
    }
}

.p-sub-mv__inner.p-sub-mv__inner--common {
    margin-top: rem(40);

    @include mq(md) {
        margin-top: rem(66);
    }
}

.p-sub-mv__inner.p-sub-mv__inner--maker {
    margin-top: rem(40);

    @include mq(md) {
        margin-top: rem(85);
        max-width: rem(1160);
        padding-inline: rem(30);
    }
}

.p-sub-mv__container {
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, 180px) minmax(auto, 180px) minmax(rem(30), 1fr);
    grid-template-rows: repeat(3, auto);

    @include mq("md") {
        --padding-inline: clamp(3.75rem, -40.089rem + 55.95vw, 13.625rem);
        /* パディングの計算を変更 */
        --container-max: 1333px;
        --viewport-width: 100vw;
        --side-space: max(0px, calc((var(--viewport-width) - var(--container-max)) / 2));
        grid-template-columns: var(--side-space) var(--padding-inline) 1fr var(--padding-inline) var(--side-space);
        grid-template-rows: repeat(3, auto);
    }
}

.p-sub-mv__container.p-sub-mv__container--maker {
    @include mq("md") {
      display: block;
    }
}

.p-sub-mv__title-wrap {
    display: contents;

    @include mq("md") {
        display: flex;
        gap: rem(12);
        grid-area: 1 / 2 / 2 / 4;
        padding-left: rem(117);
    }
}

.p-sub-mv__text-wrap {
    grid-area: 1 / 2 / 2 / 4;

    @include mq("md") {
        grid-area: 1 / 1 / 2 / 4;
        display: flex;
        gap: rem(23);
        flex-direction: column;
    }
}

.p-sub-mv__title {
    position: relative;
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-white);
    align-content: center;
    line-height: 1;
    grid-area: 1 / 2 / 2 / 4;

    @include mq(md) {
        font-size: rem(24);
        flex-shrink: 0;
    }
}

.p-sub-mv__title.p-sub-mv__title--common {
    font-size: rem(22);

    @include mq("md") {
        font-size: rem(42);
    }
}

.p-sub-mv__subtitle {
    font-size: rem(42);
    font-family: var(--baskervville);
    font-weight: var(--regular);
    color: var(--color-blue-dark);
    letter-spacing: 0.03em;
    line-height: calc(40 / 42);
    text-align: right;
    text-transform: uppercase;
    grid-area: 1 / 2 / 2 / 4;

    @include mq(md) {
        text-align: left;
        white-space: nowrap;
        font-size: rem(100);
    }
}

.p-sub-mv__sub-text {
    margin-top: rem(10);

    @include mq(md) {
        margin-top: initial;
    }
}

.p-sub-mv__image {
    position: relative;
    margin-top: rem(33);
    grid-area: 2 / 1 / 4 / 4;

    @include mq(md) {
        margin-top: rem(48);
        grid-area: 2 / 1 / 4 / 4;
    }
}

.p-sub-mv__image.p-sub-mv__image--common {
    margin-top: rem(52);

    @include mq(md) {
        margin-top: rem(51);
    }
}

.p-sub-mv__image img {
    aspect-ratio: 345 / 136;
    object-fit: cover;
    width: 100%;
    height: auto;

    @include mq("md") {
        aspect-ratio: 1218 / 316;
    }
}

.p-sub-mv__bg {
    grid-area: 3 / 1 / 4 / 5;
    border-image: linear-gradient(var(--color-white) 0 0) fill 0 / /0 100vi;
    height: rem(67);

    @include mq("md") {
        height: rem(130);
        grid-area: 3 / 1 / 4 / 6;
    }
}