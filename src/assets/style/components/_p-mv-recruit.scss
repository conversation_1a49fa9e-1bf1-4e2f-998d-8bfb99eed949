@use "../globals/"as *;

.p-mv-recruit {
    position: relative;
    background-color: var(--color-white);
    padding-top: rem(97);
    overflow-x: clip;

    @include mq("md") {
        padding-top: rem(10);
    }
}

.p-mv-recruit__vertical-text {
    display: none;

    @include mq("md") {
        display: block;
        font-size: rem(14);
        font-weight: var(--medium);
        font-family: var(--jost);
        color: var(--color-green-bright);
        line-height: calc(28 / 14);
        writing-mode: vertical-rl;
        text-transform: uppercase;
    }
}

.p-mv-recruit__vertical-text-left {
    @include mq("md") {
        position: absolute;
        top: 56%;
        left: rem(17);
        transform: translateY(-50%) rotate(180deg);
        z-index: 10;
    }
}

.p-mv-recruit__vertical-text-right {
    @include mq("md") {
        position: absolute;
        top: 56%;
        right: rem(17);
        transform: translateY(-50%);
        z-index: 10;
    }
}

.p-mv-recruit::before {
    @include mq("md") {
        content: "";
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: calc(calc(700 / 1536) * 100%);
        aspect-ratio: 700 / 312;
        z-index: 1;
        background-image: image-set(url('/images/bg_recruit.webp') type('image/webp'),
                url('/images/bg_recruit.png') type('image/png'));
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
}

.p-mv-recruit__inner {
    @include mq("md") {}
}

.p-mv-recruit__container {
    display: grid;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, 500px) minmax(rem(30), 1fr);
    grid-template-rows: auto auto;

    @include mq(md) {
        position: relative;
        display: block;
    }
}

.p-mv-recruit__container::before {
    content: "";
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--color-green-bright);
    height: rem(260);
    @include mq("md") {
        height: rem(129);
    }
}

.p-mv-recruit__title {
    width: min(100%, rem(315));
    margin-inline: auto;
    grid-area: 1 / 2 / 2 / 3;

    @include mq("md") {
        margin-inline: initial;
        position: absolute;
        z-index: 10;
        top: 50%;
        left: 9.5%;
        transform: translateY(-50%);
        width: clamp-func(594);
    }
}

.p-mv-recruit__title img {
    width: 100%;
    height: auto;
    object-fit: contain;
}

.p-mv-recruit__image {
    position: relative;
    margin-top: rem(20);
    grid-column: 2 / 4;
    justify-self: end;

    @include mq("md") {
        margin-top: initial;
        margin-left: auto;
        width: calc(calc(1328 / 1536) * 100%);
    }
}


.p-mv-recruit__image img {
    aspect-ratio: 345 / 330;
    object-fit: cover;
    width: 100%;
    height: auto;

    @include mq("md") {
        aspect-ratio: 1328 / 762;
    }
}

.p-mv-recruit__cta-wrapp {
    position: fixed;
    bottom: 0;
    right: 51%;
    transform: translateX(50%);
    display: block;
    width: 100%;
    max-width: rem(375);
    z-index: 1;

    @include mq(md) {
        right: 0;
        max-width: rem(238);
    }
}

.p-mv-recruit__cta {
    transition: filter 0.3s ease;
}

.p-mv-recruit__cta img {
    width: 101%;
    height: auto;
    object-fit: contain;
}

.p-mv-recruit__cta:hover {
    filter: brightness(1.8);
}