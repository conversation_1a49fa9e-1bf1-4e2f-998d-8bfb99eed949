@use "../globals/"as *;

.p-greeting {
    position: relative;
    background-color: var(--color-blue-navy);
    padding-block: 0 rem(76);

    @include mq(md) {
        padding-block: 0 rem(157);
    }
}

.p-greeting__inner {
    position: relative;
    z-index: 1;
    margin-inline: auto;

    @include mq("md") {
        max-width: rem(1600);
    }
}

.p-greeting__inner::after {
    @include mq("md") {
        content: "top\A message";
        position: absolute;
        bottom: rem(-44);
        right: calc(50% - min(48vw, rem(731)));
        color: var(--color-blue-dark);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        text-transform: uppercase;
        font-size: rem(136);
        line-height: calc(128 / 136);
        letter-spacing: 0.01em;
        z-index: -1;
        text-align: right;
        white-space: pre-line;
    }
}

.p-greeting__container {
    display: grid;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, rem(500)) minmax(rem(30), 1fr);
    grid-template-rows: repeat(4, auto);

    @include mq("md") {
        margin-top: rem(42);
        --padding-inline: clamp(1.875rem, -40.089rem + 55.95vw, 13.625rem);
        --grid-gap: clamp(1.25rem, -11.365rem + 18.35vw, 6.25rem);
        grid-template-columns: var(--padding-inline) var(--grid-gap) 360fr 40fr 500fr var(--grid-gap) var(--padding-inline);
        grid-template-rows: repeat(3, auto);

    }
}

.p-greeting__image {
    margin-top: rem(-94);
    grid-area: 1 / 2 / 3 / 4;
    position: relative;
    padding-left: rem(20);

    @include mq(md) {
        margin-top: rem(-59);
        padding-left: rem(5);
        grid-area: 1 / 5 / 3 / 8;
    }
}

.p-greeting__image img {
    aspect-ratio: 325 / 206;
    object-fit: cover;
    width: 100%;
    height: auto;

    @include mq("md") {
        aspect-ratio: 814 / 450;
    }
}

.p-greeting__lead-wrap {
    margin-top: rem(-2);
    grid-area: 2 / 2 / 4 / 3;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: rem(5);

    @include mq("md") {
        margin-top: initial;
        padding-bottom: rem(61);
        grid-area: 2 / 3 / 3 / 6;
        gap: rem(9);
    }
}

.p-greeting__lead-wrap--pc {
    display: none;

    @include mq("md") {
        display: inline-flex;
    }
}

.p-greeting__lead-wrap--sp {
    display: inline-flex;

    @include mq("md") {
        display: none;
    }
}

.p-greeting__lead {
    background-color: var(--color-white);
    width: fit-content;
    padding: rem(10.5) rem(5);
    margin-right: auto;
    font-size: rem(26);
    font-weight: var(--semibold);
    font-family: var(--noto-serif-jp);
    color: var(--color-dark-gray);
    line-height: 1;
    letter-spacing: 0.04em;
    display: flex;
    align-items: center;

    @include mq(md) {
        font-size: rem(60);
        padding: rem(18) rem(5);
    }

    // Safari専用のフォントサイズ調整
    @supports (-webkit-appearance: none) and (not (text-size-adjust: none)) {
        font-size: clamp(1.375rem, -0.875rem + 10vw, 1.625rem);

        @include mq("md") {
            font-size: rem(60);
        }
    }
}

.p-greeting__title-wrap {
    margin-top: rem(30);
    grid-area: 4 / 2 / 5 / 3;

    @include mq("md") {
        margin-top: rem(62);
        grid-area: 3 / 3 / 4 / 4;
    }
}

.p-greeting__content {
    margin-top: rem(26);
    grid-area: 5 / 2 / 6 / 3;
    display: grid;
    gap: rem(10);

    @include mq(md) {
        margin-top: rem(59);
        gap: rem(14);
        grid-area: 3 / 5 / 5 / 6;
    }
}

.p-greeting__message {
    margin-top: rem(-2);
    text-align: right;

}