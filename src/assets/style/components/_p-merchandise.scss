@use "../globals/"as *;

.p-merchandise {
    position: relative;
    background-color: var(--color-blue-navy);
    padding-block: rem(81) rem(80);

    @include mq(md) {
        padding-block: rem(39) rem(161);
    }
}

.p-merchandise__inner {
    position: relative;

    @include mq("md") {
        z-index: 1;
    }
}

.p-merchandise__inner::after {
    @include mq("md") {
        content: "merchandise";
        position: absolute;
        bottom: rem(-47);
        right: calc(50% - min(48vw, rem(730)));
        color: var(--color-blue-dark);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        text-transform: uppercase;
        font-size: rem(136);
        line-height: calc(128 / 136);
        letter-spacing: 0.01em;
        z-index: -1;
        text-align: right;
    }
}

.p-merchandise__container {
    @include mq("md") {
        max-width: rem(900);
        width: 100%;
        margin-inline: auto;
    }
}

.p-merchandise__title-wrap {
    text-align: left;

    @include mq("md") {
        display: grid;
        gap: rem(10);
        width: calc(calc(360 / 900) * 100%);
    }
}

.p-merchandise__title-wrap.c-section-title-wrap {
    @include mq("md") {
        padding-bottom: rem(40);
    }
}

.p-merchandise__content {

    @include mq(md) {
        margin-top: rem(59);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
}

.p-merchandise__text-wrap {
    margin-top: rem(27);
    display: grid;
    gap: rem(2);
    text-align: left;

    @include mq(md) {
        margin-top: initial;
        gap: rem(14);
        display: grid;
        width: calc(calc(500 / 900) * 100%);
    }
}

.p-merchandise__title {
    font-size: rem(18);
    font-weight: var(--bold);
    line-height: calc(30 / 18);
    letter-spacing: 0.06em;
    color: var(--color-white);

    @include mq("md") {
        font-size: rem(30);
        line-height: calc(48 / 30);
    }
}

.p-merchandise__text-wrap .p-merchandise__text:last-child {
    @include mq("md") {
        margin-top: rem(-12);
    }
}

.p-merchandise__list {
    margin-top: rem(27);
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: rem(5);

    @include mq("md") {
        margin-top: rem(57);
        grid-template-columns: repeat(5, 1fr);
        gap: rem(15);
    }
}

.p-merchandise__item {
    padding: rem(15) rem(10);
    padding: rem(8) rem(4);
    background-color: var(--color-white);
    color: var(--color-gray-dark);
    text-align: center;
    font-size: rem(14);
    font-weight: var(--bold);
    line-height: calc(24 / 14);
    letter-spacing: 0.04em;

    @include mq("md") {
        padding: rem(10) rem(4);
        font-size: rem(16);
        line-height: calc(24 / 16);
    }
}

.p-merchandise__button-wrap {
    margin-top: rem(31);

    @include mq(md) {
        margin-top: rem(40);
        margin-left: auto;
        max-width: rem(270);
    }
}