@use "../globals/"as *;

.p-company-access {
    background-color: var(--color-white);
    padding-bottom: rem(80);

    @include mq(md) {
        padding-bottom: rem(93);
    }
}


.p-company-access__inner.l-inner {
    @include mq(md) {
        max-width: rem(950);
        margin-inline: auto;
    }
}

.p-company-access__container {
    @include mq("md") {
        position: relative;
        z-index: 0;
        display: grid;
        grid-template-columns: 180fr 650fr;
        align-items: flex-start;
        gap: rem(70);
    }
}

.p-company-access__container::after {
    @include mq("md") {
        content: "access";
        position: absolute;
        top: rem(-47);
        left: calc(50% - min(48vw, rem(690)));
        color: var(--color-off-white);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        text-transform: uppercase;
        font-size: rem(136);
        line-height: calc(128 / 136);
        letter-spacing: 0.01em;
        z-index: -1;
        text-align: right;
        white-space: pre-line;
    }
}

.p-company-access__title-wrap.c-section-title-wrap {
    @include mq("md") {
        padding-bottom: rem(40);
    }
}

.p-company-access__content {
    margin-top: rem(28);
    display: grid;
    gap: rem(59);

    @include mq(md) {
        margin-top: initial;
        grid-template-columns: 1fr;
        gap: rem(35);
    }
}

.p-company-access__location {
    display: flex;
    flex-direction: column;
    gap: rem(24);

    @include mq(md) {
        flex-direction: row;
        align-items: flex-end;
        gap: rem(40);
    }
}

.p-company-access__map {
    @include mq("md") {
        width: min(100%, rem(388));
        flex-shrink: 0;
    }
}

.p-company-access__map iframe {
    aspect-ratio: 315/196;
    object-fit: cover;
    width: 100%;
    height: auto;

    @include mq(md) {
        aspect-ratio: 388/242;
    }
}

.p-company-access__info {
    @include mq("md") {
        padding-bottom: rem(5);
    }
}

.p-company-access__name {
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    letter-spacing: 0.04em;
    line-height: calc(28 / 18);
    border-bottom: 1px solid var(--color-gray-medium-2);
    padding-bottom: rem(5);

    @include mq(md) {
        font-size: rem(24);
        line-height: calc(28 / 24);
    }
}

.p-company-access__address {
    margin-top: rem(15);
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-black-2);
    line-height: calc(24 / 16);
    letter-spacing: 0.04em;
    border: none;
    text-decoration: none;

    @include mq(md) {
        margin-top: rem(16);
    }
}

.p-company-access__contact {
    margin-top: rem(8);
    display: flex;
    flex-direction: column;
    gap: rem(4);
}


.p-company-access__tel,
.p-company-access__fax,
.p-company-access__email {
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-black-2);
    letter-spacing: 0.04em;
    line-height: calc(24 / 16);
    margin-left: rem(-8);
    border: none;
    text-decoration: none;

    @include mq(md) {
        margin-top: initial;
        white-space: nowrap;
    }
}

.p-company-access__tel a {
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-black-2);
    letter-spacing: 0.04em;
    line-height: calc(24 / 16);
    cursor: pointer;
    border: none;
    text-decoration: none;
}

.p-company-access__tel span,
.p-company-access__fax span,
.p-company-access__email span {
    position: relative;
    color: var(--color-gray-dark);
    font-weight: var(--bold);
    display: inline-flex;
    gap: rem(6);

    @include mq("md") {
        gap: rem(3);
    }
}

.p-company-access__tel span::before,
.p-company-access__fax span::before,
.p-company-access__email span::before {
    content: "【";
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--color-gray-dark);
}

.p-company-access__tel span::after,
.p-company-access__fax span::after,
.p-company-access__email span::after {
    content: "】";
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--color-gray-dark);
    margin-right: rem(-3);
}