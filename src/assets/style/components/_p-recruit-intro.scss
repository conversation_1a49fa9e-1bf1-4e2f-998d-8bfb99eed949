@use "../globals/"as *;

.p-recruit-intro {
    padding: rem(40) rem(20);
    background-color: #fff;
}

.p-recruit-intro__header {
    text-align: center;
    margin-bottom: rem(30);
}

.p-recruit-intro__lead {
    font-size: rem(28);
    font-weight: 500;
    color: #0baa00;
    letter-spacing: 0.05em;
    line-height: 1.4;
}

.p-recruit-intro__sub {
    font-size: rem(12);
    font-weight: 400;
    color: #0baa00;
    letter-spacing: 0.05em;
    margin-top: rem(4);
}

.p-recruit-intro__tabs {
    display: flex;
    justify-content: center;
    gap: rem(10);
    margin-bottom: rem(40);
}

.p-recruit-intro__tab {
    padding: rem(10) rem(16);
    background-color: #f3f3f3;
    font-size: rem(14);
    font-weight: 500;
    color: #0baa00;
    text-align: center;
    line-height: 1.5;
    cursor: pointer;
}

.p-recruit-intro__tab span {
    display: block;
    font-size: rem(10);
    color: #999;
}

.p-recruit-intro__tab.is-active {
    background-color: #fff;
    border: rem(2) solid #0baa00;
}

.p-recruit-intro__block {
    display: flex;
    flex-direction: column;
    gap: rem(60);
}

.p-recruit-intro__content {
    display: flex;
    flex-direction: column;
    gap: rem(24);
}

.p-recruit-intro__content.is-show {
    display: flex;
}

@include mq("md") {
    .p-recruit-intro__content {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: rem(40);
    }
}

.p-recruit-intro__text-area {
    flex: 1;
}

.p-recruit-intro__title {
    font-size: rem(24);
    font-weight: 600;
    color: #0baa00;
    letter-spacing: 0.05em;
    line-height: 1.5;
    margin-bottom: rem(8);
}

.p-recruit-intro__title span {
    font-size: rem(10);
    font-weight: 400;
    color: #999;
    margin-left: rem(10);
}

.p-recruit-intro__date {
    font-size: rem(12);
    font-weight: 500;
    letter-spacing: 0.05em;
    color: #0baa00;
    display: block;
    margin-bottom: rem(12);
}

.p-recruit-intro__catch {
    font-size: rem(16);
    font-weight: 700;
    color: #000;
    letter-spacing: 0.05em;
    line-height: 1.6;
    margin-bottom: rem(16);
}

.p-recruit-intro__desc {
    font-size: rem(14);
    font-weight: 400;
    color: #333;
    letter-spacing: 0.05em;
    line-height: 1.8;
}

.p-recruit-intro__desc mark {
    background-color: transparent;
    color: #0baa00;
    font-weight: 500;
}

.p-recruit-intro__image {
    flex: 0 0 rem(240);
}

@include mq("md") {
    .p-recruit-intro__image {
        flex: 0 0 rem(520);
    }
}

.p-recruit-intro__image img {
    width: 100%;
    height: auto;
    object-fit: cover;
}
