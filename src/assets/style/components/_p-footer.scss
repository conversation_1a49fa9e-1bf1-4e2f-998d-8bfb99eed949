@use "../globals/"as *;



.p-footer {
    background-color: var(--color-blue-navy);
}

.p-footer.p-footer--recruit {
    background-color: var(--color-green-bright);
}

.p-footer__banner {
    background-image: image-set(url('/images/bg_footer.webp') type('image/webp'),
            url('/images/bg_footer.jpg') type('image/jpeg'));
    background-size: cover;
    background-position: center;
    padding: rem(30) rem(30);

    @include mq(md) {
        padding: rem(80) rem(20);
    }
}

.p-footer__recruit-banner {
    background-image: image-set(url('/images/bg_recruit-banner.webp') type('image/webp'),
            url('/images/bg_recruit-banner.jpg') type('image/jpeg'));
    background-size: cover;
    background-position: center;
    padding: rem(30) rem(30);

    @include mq(md) {
        padding: rem(80) rem(20);
    }
}

.p-footer__banner-inner {
    display: flex;
    flex-direction: column;
    max-width: rem(540);
    margin-inline: auto;

    @include mq(md) {
        max-width: rem(1100);
        flex-direction: row;
        justify-content: center;
    }
}

.p-footer__recruit-banner-wrap {
    position: relative;
}

.p-footer__recruit-banner-check {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    display: inline-block;
    width: rem(200);
    margin-bottom: rem(10);
    padding: rem(4) rem(16);
    border: 1px solid var(--color-dark-gray);
    border-radius: rem(25);
    background-color: var(--color-yellow);
    text-align: center;
    font-size: rem(12);
    font-weight: var(--medium);
    line-height: calc(18 / 12);
    color: var(--color-dark-gray);
}

.p-footer__recruit-banner-check::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 100%);
    border-style: solid;
    border-width: rem(10) rem(6.5) 0 rem(6.5);
    border-color: var(--color-dark-gray) transparent transparent;
}

.p-footer__recruit-banner-check::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 100%);
    border-style: solid;
    border-width: rem(8.2) rem(5.3) 0 rem(5.3);
    border-color: var(--color-yellow) transparent transparent;
}

.p-footer__recruit-banner-text-wrap {
    background-color: var(--color-dark-gray);
    padding: rem(10) rem(20);
    border-radius: rem(20);
    display: flex;
    align-items: center;
    justify-content: center;

    @include mq("md") {
        padding: rem(10) rem(20);
    }
}

.p-footer__recruit-banner-text {
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-white);
    letter-spacing: 0.06em;
    line-height: calc(24 / 18);

    @include mq("md") {
        font-size: rem(24);
        line-height: calc(32 / 24);
    }
}

.p-footer__banner-block {
    position: relative;
    flex: 1;
    text-align: center;
    background-color: var(--color-blue-navy);
    padding: rem(27) rem(10) rem(15);
    overflow: hidden;

    @include mq(md) {
        padding: rem(41) rem(20);
    }
}

.p-footer__banner-block:hover {
    opacity: 1;
}

.p-footer__banner-block::after {
    content: "";
    position: absolute;
    z-index: 5;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-orange);
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    z-index: 0;
}

.p-footer__banner-block:hover::after {
    transform: translateX(0);
}


.p-footer__banner-block.p-footer__banner-block--right {
    border-top: 1px solid var(--color-white);

    @include mq("md") {
        position: relative;
        border-top: none;
    }
}

.p-footer__banner-block.p-footer__banner-block--right::before {
    @include mq("md") {
        content: "";
        position: absolute;
        z-index: 10;
        top: 0;
        left: 0;
        width: 1px;
        height: 100%;
        background-color: var(--color-white);
    }
}


.p-footer__banner-title-ja {
    position: relative;
    z-index: 1;
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-white);
    letter-spacing: 0.06em;
    line-height: 1;

    @include mq(md) {
        font-size: rem(30);
    }
}

.p-footer__banner-title-ja::before {
    content: attr(data-title);
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--color-blue-dark);
    font-family: var(--baskervville);
    font-weight: var(--regular);
    font-size: rem(58);
    line-height: 1.3;
    letter-spacing: 0.03em;
    text-transform: uppercase;
    white-space: nowrap;

    @include mq("md") {
        font-size: clamp-func(100);
    }
}

.p-footer__banner-title-en {
    position: relative;
    z-index: 10;
    font-size: rem(14);
    font-family: var(--baskervville);
    font-weight: var(--regular);
    color: var(--color-white);
    letter-spacing: 0.06em;
    margin-top: rem(10);
    text-transform: uppercase;
    line-height: 1.5;

    @include mq(md) {
        font-size: rem(16);
        margin-top: rem(19);
    }
}

.p-footer__inner {
    padding: rem(43) rem(30) rem(20);

    @include mq(md) {
        padding: rem(82) rem(25) rem(20);
    }
}

.p-footer__top {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: rem(30);

    @include mq(md) {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
}

.p-footer__logo {
    max-width: rem(315);
    width: 100%;

    @include mq(md) {
        max-width: rem(370);
    }
}

.p-footer__logo img {
    width: 100%;
    height: auto;
    object-fit: contain;
}

.p-footer__contact {
    width: min(100%, rem(231));

    @include mq("md") {
        margin-top: rem(-13);
    }
}

.p-footer__contact-icon {}

.p-footer__contact-icon::before {
    font-size: rem(20);
}

.p-footer__content {
    margin-top: rem(18);
    display: flex;
    flex-direction: column;
    gap: rem(40);

    @include mq(md) {
        margin-top: rem(24);
        flex-direction: row;
        gap: rem(60);
        align-items: flex-start;
        justify-content: space-between;
    }
}

.p-footer__offices {
    display: flex;
    flex-direction: column;
    gap: rem(30);

    @include mq(md) {
        flex-direction: row;
        gap: rem(32);
        flex-shrink: 0;
        width: calc(calc(564 / 1100) * 100%);
    }
}

.p-footer__office {
    min-width: rem(200);

    @include mq("md") {
        max-width: rem(264);
        width: 100%;
    }
}

.p-footer__office-title {
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-white);
    letter-spacing: 0.04em;
    line-height: calc(34 / 18);
    padding-bottom: rem(6);
    border-bottom: 1px solid var(--color-white);
}

.p-footer__office-address {
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(24 / 16);
    color: var(--color-white);
    letter-spacing: 0.04em;
    margin-top: rem(8);
    white-space: nowrap;
    border: none;
    text-decoration: none;
}

.p-footer__office-contact-wrap {
    margin-top: rem(18);
    display: flex;
    flex-direction: column;
    gap: rem(8);
}

.p-footer__office-tel,
.p-footer__office-fax {
    font-size: rem(22);
    font-weight: var(--bold);
    line-height: 1;
    color: var(--color-white);
    letter-spacing: 0.04em;
    border: none;
    text-decoration: none;
}



.p-footer__map {
    margin-top: rem(25);
    padding: rem(6) rem(29);
    display: inline-block;
    font-size: rem(16);
    color: var(--color-white);
    font-weight: var(--bold);
    line-height: 1;
    letter-spacing: 0.04em;
    border: 1px solid var(--color-white);
    border-radius: rem(20);
    transition: background-color 0.3s, color 0.3s;
}

.p-footer__map:hover {
    background-color: var(--color-white);
    color: var(--color-dark-gray);
}

.p-footer__nav-sp {
    display: flex;
    flex-direction: column;

    @include mq(md) {
        display: none;
    }
}

.p-footer__nav-pc {
    display: none;

    @include mq(md) {
        display: block;
        width: calc(calc(439 / 1100) * 100%);
    }
}

.p-footer__nav-columns {
    @include mq("md") {
        display: flex;
        gap: rem(20);
        justify-content: space-between;
    }
}

.p-footer__nav-column {
    position: relative;
}

.p-footer__nav-column:nth-child(2) {
    @include mq("md") {
        max-width: rem(120);
    }
}

.p-footer__nav-column:nth-child(3) {
    @include mq("md") {
        max-width: rem(70);
    }
}

.p-footer__nav-column:nth-child(2)::before {
    @include mq("md") {
        content: "";
        position: absolute;
        top: 0;
        left: -26%;
        width: 1px;
        height: 100%;
        background-color: var(--color-white);
    }
}

.p-footer__nav-column:nth-child(3)::before {
    @include mq("md") {
        content: "";
        position: absolute;
        top: 0;
        left: -43%;
        width: 1px;
        height: 100%;
        background-color: var(--color-white);
    }
}

.p-footer__nav-wrap {
    padding-block: rem(18);
    display: grid;
    gap: rem(3);
    border-bottom: 1px solid var(--color-white);

    @include mq("md") {
        padding-block: initial;
        border-bottom: none;
    }
}

.p-footer__nav-wrap.p-footer__nav-wrap--top {
    @include mq("md") {
        margin-top: rem(8);
    }
}

.p-footer__nav-wrap.p-footer__nav-wrap--title {
    @include mq("md") {
        margin-top: rem(20);
    }
}

.p-footer__nav-policy {
    margin-top: rem(20);

    @include mq("md") {
        margin-top: rem(12);
        text-align: right;
    }
}

.p-footer__nav-title {
    font-size: rem(16);
    font-weight: var(--bold);
    color: var(--color-white);
    line-height: 1.5;
    letter-spacing: 0.06em;
}

.p-footer__nav-title.p-footer__nav-title--top {
    @include mq("md") {
        margin-top: rem(20);
    }
}


.p-footer__nav-link {
    font-size: rem(12);
    color: var(--color-white);
    font-weight: var(--medium);
    line-height: calc(20 / 12);
    letter-spacing: 0.04em;
}

.p-footer__nav-list {}

.p-footer__nav-item {
    border-bottom: 1px solid var(--color-white);
}

.p-footer__nav-item.p-footer__nav-item--recruit {
    padding-block: rem(10);
}

.p-footer__nav-item a {
    color: var(--color-white);
    font-size: rem(16);
    font-weight: var(--bold);
    line-height: 1.5;
    letter-spacing: 0.06em;
    padding-bottom: rem(6);
    transition: color 0.3s;

}

.p-footer__nav-item a:hover {
    color: var(--color-dark-gray);
}

.p-footer__nav-button {
    margin-top: rem(40);

    @include mq("md") {
        margin-top: rem(40);
    }
}

.p-footer__nav-button {
    padding: rem(17) rem(20);
    max-width: rem(266);
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: calc(22 / 16);
    font-size: rem(16);
    font-weight: var(--bold);
    color: var(--color-blue);
    background-color: var(--color-white);
    letter-spacing: 0.04em;
    border-radius: rem(30);
    transition: opacity 0.3s ease, border-radius 0.3s ease, background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        font-size: rem(18);
        padding: rem(22) rem(25);
        max-width: rem(270);
    }
}



.p-footer__bottom {
    margin-top: rem(40);
    text-align: center;

    @include mq(md) {
        margin-top: rem(117);
    }
}

.p-footer__copyright {
    font-size: rem(10);
    font-weight: var(--medium);
    color: var(--color-white);
    line-height: 1;
}