@use "../globals/"as *;

.p-philosophy {
    position: relative;
    padding-block: rem(81) rem(80);

    @include mq(md) {
        padding-block: rem(101) rem(161);
    }
}

.p-philosophy__inner {
    position: relative;
    margin-inline: auto;

    @include mq("md") {
        max-width: rem(1600);
    }
}

.p-philosophy__inner::before {
    @include mq("md") {
        content: "philosophy";
        position: absolute;
        bottom: rem(79);
        left: 50%;
        transform: translateX(-50%);
        color: var(--color-off-white);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        text-transform: uppercase;
        font-size: rem(136);
        line-height: calc(128 / 136);
        letter-spacing: 0.01em;
        z-index: -1;
        text-align: right;
    }
}

.p-philosophy__inner::after {
    @include mq("md") {
        content: "and policy";
        position: absolute;
        bottom: rem(-48);
        left: 50%;
        transform: translateX(-50%);
        color: var(--color-off-white);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        text-transform: uppercase;
        font-size: rem(136);
        line-height: calc(128 / 136);
        letter-spacing: 0.01em;
        z-index: -1;
        text-align: right;
        white-space: nowrap;
    }
}

.p-philosophy__container {
    display: grid;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, rem(500)) minmax(rem(30), 1fr);
    grid-template-rows: repeat(4, auto);

    @include mq("md") {
        --padding-inline: clamp(1.875rem, -40.089rem + 55.95vw, 13.625rem);
        grid-template-columns: 218fr 100fr 500fr 400fr 100fr 218fr;
        grid-template-rows: repeat(3, auto);

    }
}

.p-philosophy__image {
    grid-area: 1 / 1 / 3 / 3;
    position: relative;
    padding-right: rem(20);

    @include mq(md) {
        grid-area: 1 / 1 / 3 / 4;
        padding-right: rem(5);
    }
}

.p-philosophy__image img {
    aspect-ratio: 325 / 206;
    object-fit: cover;
    width: 100%;
    height: auto;

    @include mq("md") {
        aspect-ratio: 814 / 450;
    }
}

.p-philosophy__lead-wrap {
    grid-area: 2 / 2 / 4 / 3;
    padding-top: rem(47);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: rem(5);

    @include mq("md") {
        grid-area: 1 / 3 / 3 / 5;
        padding-top: rem(189);
        gap: rem(9);
    }
}


.p-philosophy__lead {
    background-color: var(--color-blue-navy);
    width: fit-content;
    padding: rem(10.5) rem(4);
    margin-left: auto;
    font-size: rem(26);
    font-weight: var(--semibold);
    font-family: var(--noto-serif-jp);
    color: var(--color-white);
    line-height: 1;
    letter-spacing: 0.04em;
    display: flex;
    align-items: center;

    @include mq(md) {
        font-size: rem(60);
        padding: rem(18) rem(5);
    }
}

.p-philosophy__title-wrap {
    text-align: center;

    @include mq("md") {
        display: grid;
        gap: rem(10);
    }
}

.p-philosophy__content {
    grid-area: 4 / 2 / 5 / 3;

    @include mq(md) {
        margin-top: rem(59);
        text-align: right;
        grid-area: 3 / 3 / 4 / 5;
    }
}

.p-philosophy__text-wrap {
    margin-top: rem(27);
    display: grid;
    gap: rem(10);
    text-align: center;

    @include mq(md) {
        margin-top: rem(41);
        gap: rem(23);
    }
}

.p-philosophy__logo {
    margin-top: rem(23);
    max-width: rem(255);
    margin-inline: auto;

    @include mq("md") {
        margin-top: rem(35);
        max-width: rem(340);
    }
}

.p-philosophy__logo img {
    width: 100%;
    height: auto;
    object-fit: contain;
}