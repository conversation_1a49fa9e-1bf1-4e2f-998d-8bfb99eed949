@use "../globals/"as *;

.p-top-news {
    padding-block: rem(83) rem(81);

    @include mq("md") {
        padding-block: rem(60) rem(81);
    }
}

.p-top-news__inner {

    @include mq("md") {}
}

.p-top-news__container {
    @include mq("md") {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
}

.p-top-news__container::after {
    @include mq("md") {
        content: "news";
        position: absolute;
        right: calc(50% - min(48vw, rem(720)));
        top: rem(-127);
        color: var(--color-off-white);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        font-size: rem(100);
        line-height: 1;
        text-transform: uppercase;
        white-space: nowrap;
    }
}

.p-top-news__title-wrap {
    text-align: left;
    display: grid;
    gap: rem(10);
}

.p-top-news__list-wrap {
    margin-top: rem(8);

    @include mq("md") {
        margin-top: rem(-22);
        width: calc(calc(800 / 1100) * 100%);
    }
}

.p-top-news__item a {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: rem(2);
    padding: rem(19) 0;
    border-bottom: 1px solid var(--color-gray-medium-2);
    position: relative;

    @include mq("md") {
        flex-direction: row;
        align-items: center;
        padding: rem(20) 0 rem(14);
        gap: rem(30);
        transition: background-color 0.3s $easeOutQuart;
    }
}

.p-top-news__item a:hover {
    background-color: #f5f5f5;
    opacity: 1;
}

.p-top-news__item a::after {

    @include mq("md") {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background-color: var(--color-gray-medium-2);
        clip-path: $clip-triangle-right;
        width: rem(10);
        height: rem(10);
    }
}

.p-top-news__label-wrap {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: rem(10);

    @include mq("md") {
        justify-content: center;
    }
}

.p-top-news__label {
    padding: rem(1) rem(2) rem(2);
    min-width: rem(61);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: rem(12);
    font-weight: var(--medium);
    color: var(--color-white);
    background-color: var(--color-gray-medium);
    line-height: calc(18 / 12);
    border-radius: rem(2);
}

.p-top-news__date {
    font-size: rem(16);
    font-family: var(--noto-serif-jp);
    font-weight: var(--regular);
    color: var(--color-gray-medium);
    line-height: calc(18 / 12);

}

.p-top-news__text {
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-dark-gray);
    letter-spacing: 0.04em;
    line-height: calc(26 / 16);
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    @include mq("md") {
        -webkit-line-clamp: 1;

    }
}

.p-top-news__button-wrap {
    margin-top: rem(40);
    width: min(100%, rem(240));

    @include mq("md") {
        margin-top: rem(35);
        margin-left: rem(115);
        width: min(100%, rem(270));
    }
}