@use "../globals/"as *;

.p-news-detail {
    background-color: var(--color-white);
    padding-block: rem(60) rem(80);

    @include mq(md) {
        padding-block: rem(100) rem(100);
    }
}

.p-news-detail__inner.l-inner {
    margin-top: rem(45);

    @include mq("md") {
        margin-top: rem(154);
        max-width: rem(950);
        width: 100%;
        margin-inline: auto;
    }
}

.p-news-detail__container {
    width: 100%;
}

/* 日付表示ボックス */
.p-news-detail__date-box {
    display: flex;
    align-items: center;
    gap: rem(8);
}

.p-news-detail__category-label {
    background-color: var(--color-gray-medium);
    color: var(--color-white);
    padding: rem(4) rem(12);
    font-size: rem(12);
    font-weight: var(--medium);
    line-height: 1;
    border-radius: rem(2);

    @include mq("md") {
        padding: rem(4) rem(10.5);
    }
}

.p-news-detail__date-value {
    font-size: rem(16);
    color: var(--color-gray-medium);
    font-family: var(--noto-serif-jp);
    font-weight: var(--regular);
    line-height: 1;

}

/* 記事タイトル */
.p-news-detail__title {
    margin-top: rem(10);
    font-size: rem(22);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: calc(33 / 23);
    letter-spacing: 0.06em;

    @include mq("md") {
        margin-top: rem(10);
        font-size: rem(34);
        line-height: calc(54 / 34);
        letter-spacing: 0.04em;
    }
}

/* 記事本文 */
.p-news-detail__content {
    color: var(--color-dark-gray);
    margin-top: rem(24);

    @include mq("md") {
        margin-top: rem(53);
    }
}

/* 段落 */
.p-news-detail__content p {
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;

    @include mq("md") {
        margin-top: rem(30);
    }
}

.p-news-detail__content p:not(:first-child) {
    margin-top: rem(30);
}

.p-news-detail__content p>strong {
    color: var(--color-orange-dark);
    font-weight: var(--medium);
    font-size: rem(16);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;
}

/* 見出し2 */
.p-news-detail__content h2 {
    margin-top: rem(60);
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: calc(30 / 18);
    letter-spacing: 0.06em;
    border-top: 1px solid var(--color-gray-dark);
    border-bottom: 1px solid var(--color-gray-dark);
    padding-block: rem(15) rem(11);

    @include mq("md") {
        font-size: rem(30);
        line-height: calc(45 / 30);
        letter-spacing: 0.05em;
        padding-block: rem(16) rem(20);
    }
}

/* 見出し3 */
.p-news-detail__content h3 {
    margin-top: rem(60);
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-gray-dark);
    background-color: var(--color-off-white);
    line-height: calc(30 / 18);
    letter-spacing: 0.04em;
    padding: rem(20);
    border-left: rem(4) solid var(--color-orange-dark);

    @include mq("md") {
        font-size: rem(20);
        line-height: calc(34 / 20);
        letter-spacing: 0.04em;
    }
}

/* 見出し4 */
.p-news-detail__content h4 {
    margin-top: rem(60);
    font-size: rem(16);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: calc(24 / 16);
    letter-spacing: 0.06em;
    border-bottom: 1px solid var(--color-gray-dark);
    padding-bottom: rem(15);

    @include mq("md") {
        font-size: rem(18);
        line-height: calc(34 / 18);
        letter-spacing: 0.04em;
    }
}

/* 見出し5 */
.p-news-detail__content h5 {
    margin-top: rem(60);
    font-size: rem(16);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: calc(24 / 16);
    letter-spacing: 0.06em;

    @include mq("md") {
        font-size: rem(18);
        line-height: calc(34 / 18);
        letter-spacing: 0.04em;
    }
}

/* 引用ボックス */
.p-news-detail__content blockquote {
    position: relative;
    margin-top: rem(30);
    background-color: var(--color-off-white);
    border: 1px solid var(--color-gray-dark);
    padding: rem(34) rem(20) rem(27);
    border-radius: rem(10);

    @include mq("md") {
        margin-top: rem(60);
        padding: rem(27) rem(30) rem(64) rem(20);
    }
}

.p-news-detail__content blockquote::before {
    content: '';
    position: absolute;
    top: rem(8);
    left: rem(10);
    width: rem(23);
    height: rem(18);
    background-image: url(/images/logo_quote.svg);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transform: rotate(180deg);

    @include mq("md") {
        top: rem(30);
        left: rem(20);
    }
}

.p-news-detail__content blockquote p {
    font-style: normal;
    font-size: rem(14);
    line-height: calc(25.2 / 14);
    letter-spacing: 0.04em;
    color: var(--color-gray-dark);
}

.p-news-detail__content blockquote p:not(:first-child) {
    margin-top: rem(5);
    font-size: rem(12);
}

/* 画像 */
.p-news-detail__content .wp-block-image {
    margin-top: rem(30);
    display: block;
    margin-inline: auto;

    @include mq("md") {
        margin-top: rem(60);
        max-width: rem(650);
    }
}


/* リスト */
.p-news-detail__content ul,
.p-news-detail__content ol {
    margin-top: rem(30);
    padding-left: 1em;
	display: flex;
	flex-direction: column;

    @include mq("md") {
		margin-top: rem(60);
        padding-left: rem(24);
        max-width: rem(715);
        width: 100%;
        margin-inline: auto;
		gap: rem(5);
    }
}

.p-news-detail__content ul {
    list-style-type: disc;
}

.p-news-detail__content ol {
    list-style-type: decimal;
}

.p-news-detail__content li {
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(28.8 / 16);
    letter-spacing: 0.04em;
}

.p-news-detail__content li::marker {
    color: var(--color-orange-dark);
    font-size: rem(12);
}

.p-news-detail__content ol li::marker {
    color: var(--color-orange-dark);
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(28.8 / 16);
    letter-spacing: 0;

    @include mq("md") {
        letter-spacing: 0.04em;
    }
}


/* ページネーション */
.p-news-detail__pagination {
    margin-top: rem(60);

    @include mq("md") {
        border-top: 1px solid var(--color-gray-darker-2);
        padding-top: rem(60);
    }
}

.p-news-detail__pagination-links {
    display: flex;
    flex-direction: column;
    align-items: center;

    @include mq("md") {
        flex-direction: row;
        max-width: rem(644);
        width: 100%;
        margin-inline: auto;
    }
}

.p-news-detail__pagination-prev,
.p-news-detail__pagination-next,
.p-news-detail__pagination-list {
    display: block;
    padding: rem(20) rem(5);
    width: 100%;
    text-align: center;
    color: var(--color-gray-darker-2);
    font-size: rem(14);
    font-weight: var(--medium);
    line-height: calc(18 / 14);
    letter-spacing: 0.04em;
    border-top: 1px solid var(--color-gray-darker-2);
    transition: color 0.3s ease;
    position: relative;

    @include mq("md") {
        padding: 0 rem(5);
        border-top: none;
    }
}

@media (hover: hover) {
    .p-news-detail__pagination-prev:hover,
    .p-news-detail__pagination-next:hover,
    .p-news-detail__pagination-list:hover {
        color: var(--color-blue);
        opacity: 1;
        text-decoration: underline;
        text-underline-offset: rem(2);
    }
}

.p-news-detail__pagination-list {
    @include mq("md") {
        position: relative;
    }
}

.p-news-detail__pagination-list::before {
    @include mq("md") {
        content: '';
        position: absolute;
        top: 35%;
        left: rem(-18);
        transform: translateY(-50%);
        width: 1px;
        height: 100%;
        background-color: var(--color-gray-darker-2);
    }
}

.p-news-detail__pagination-prev .fa-arrow-left {
    display: none;

    @include mq("md") {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        color: var(--color-gray-darker-2);
        left: rem(2);
        font-size: rem(20);
    }
}

.p-news-detail__pagination-next {
    border-bottom: 1px solid var(--color-gray-darker-2);

    @include mq("md") {
        position: relative;
        border-bottom: none;
    }
}

.p-news-detail__pagination-next::before {
    @include mq("md") {
        content: '';
        position: absolute;
        top: 50%;
        left: rem(18);
        transform: translateY(-50%);
        width: 1px;
        height: 100%;
        background-color: var(--color-gray-darker-2);
    }
}

.p-news-detail__pagination-next .fa-arrow-right {
    display: none;

    @include mq("md") {
        position: absolute;
        top: 35%;
        transform: translateY(-50%);
        color: var(--color-gray-darker-2);
        right: rem(2);
        font-size: rem(20);
    }
}