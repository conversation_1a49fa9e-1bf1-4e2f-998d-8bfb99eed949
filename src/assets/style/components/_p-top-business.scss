@use "../globals/"as *;

.p-top-business {
    position: relative;
    padding-block: rem(78) rem(81);

    @include mq(md) {
        padding-block: rem(99) rem(98);
    }
}

.p-top-business__inner {
    max-width: rem(1600);
    margin-inline: auto;
}

.p-top-business__title-wrap {
    display: grid;
    gap: rem(6);

}

.p-top-business__container {
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, 400px) minmax(rem(30), 1fr);
    grid-template-rows: auto auto;
    grid-template-areas:
        "l-margin image image"
        "l-margin text r-margin";

    @include mq("md") {
        --padding-inline: clamp(1.875rem, -27.769rem + 43.12vw, 13.625rem);
        grid-template-columns: var(--padding-inline) 500fr 597fr var(--padding-inline);
    }
}

.p-top-business__image {
    clip-path: polygon(0 0, 100% 0%, 100% 100%, 20% 100%);
    grid-area: image;
    position: relative;

    @include mq(md) {
        clip-path: polygon(0 0, 100% 0%, 100% 100%, 19% 100%);
        grid-area: 1 / 3 / 2 / 5;
    }
}

.p-top-business__image img {
    aspect-ratio: 345 / 206;
    object-fit: cover;
    width: 100%;
    height: auto;

    @include mq("md") {
        aspect-ratio: 815 / 451;
    }
}

.p-top-business__content {
    margin-top: rem(30);
    grid-area: text;

    @include mq(md) {
        margin-top: 0;
        padding-top: rem(60);
        grid-area: 1 / 2 / 2 / 3;
    }
}

.p-top-business__text {
    margin-top: rem(12);
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-dark-gray);
    letter-spacing: 0.04em;
    line-height: calc(30 / 16);

    @include mq(md) {
        margin-top: rem(27);
        font-size: clamp(0.875rem, 0.56rem + 0.46vw, 1rem);
    }
}

.p-top-business__button-wrap {
    margin-top: rem(38);

    @include mq(md) {
        margin-top: rem(37);
        margin-right: auto;
        max-width: rem(270);
    }
}

