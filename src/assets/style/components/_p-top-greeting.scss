@use "../globals/"as *;

.p-top-greeting {
    position: relative;
    background-color: var(--color-blue-navy);
    padding: rem(82) 0;

    @include mq(md) {
        padding-block: rem(157) rem(100);
    }
}

.p-top-greeting__inner {
    max-width: rem(1600);
    margin-inline: auto;
}

.p-top-greeting__title-wrap {
    text-align: center;

    @include mq("md") {
        display: grid;
        gap: rem(10);
    }
}

.p-top-greeting__container {
    margin-top: rem(28);
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, 400px) minmax(rem(30), 1fr);
    grid-template-rows: auto auto;
    grid-template-areas:
    "image image r-margin"
    "l-margin text r-margin";

    @include mq("md") {
        margin-top: rem(42);
        --padding-inline: clamp(1.875rem, -40.089rem + 55.95vw, 13.625rem);
        display: grid;
        grid-template-columns: var(--padding-inline) 379fr 218fr 503fr var(--padding-inline);
    }
}

.p-top-greeting__image {
    clip-path: polygon(0 0, 100% 0%, 76% 100%, 0% 100%);
    grid-area: image;
    position: relative;
    padding-bottom: rem(53);

    @include mq(md) {
        padding-bottom: initial;
        grid-area: 1 / 1 / 2 / 4;
    }
}

.p-top-greeting__splide {
    height: 100%;
}

.p-top-greeting__splide .splide__track {
    height: 100%;
}

.p-top-greeting__splide .splide__list {
    height: 100%;
}

.p-top-greeting__splide .splide__slide {
    height: 100%;
    // より緩やかなフェード効果のためのカスタムトランジション
    transition: opacity 1.5s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.p-top-greeting__splide .splide__slide-image {
    height: 100%;
}

.p-top-greeting__splide .splide__slide-image img {
    aspect-ratio: 345/206;
    object-fit: cover;
    width: 100%;
    height: auto;
    // 画像もスムーズに切り替わるように
    transition: opacity 1.5s cubic-bezier(0.25, 0.1, 0.25, 1);

    @include mq("md") {
        aspect-ratio: 814/450;
    }
}

.p-top-greeting__content {
    grid-area: text;

    @include mq(md) {
        padding-top: rem(40);
        text-align: right;
        grid-area: 1 / 2 / 2 / 5;
    }
}

.p-top-greeting__lead-wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: rem(5);

    @include mq("md") {
        gap: rem(9);
    }
}

.p-top-greeting__lead {
    background-color: var(--color-gray-medium-2);
    width: fit-content;
    padding: rem(10.5) rem(5);
    margin-left: auto;
    font-size: rem(26);
    font-weight: var(--semibold);
    font-family: var(--noto-serif-jp);
    color: var(--color-dark-gray);
    line-height: 1;
    letter-spacing: 0.04em;
    display: flex;
    align-items: center;

    @include mq(md) {
        font-size: rem(60);
        padding: rem(18) rem(5);
    }
}

.p-top-greeting__lead span {
    margin-left: rem(5);
    font-size: inherit;
    color: inherit;
}

.p-top-greeting__catch {
    margin-top: rem(30);
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-white);
    letter-spacing: 0.06em;
    line-height: calc(30 / 18);

    @include mq(md) {
        font-size: rem(30);
        margin-top: rem(26);
    }
}

.p-top-greeting__message {
    margin-top: rem(5);
}

.p-top-greeting__text {
    margin-top: rem(11);

    @include mq(md) {
        margin-top: rem(28);
    }
}

.p-top-greeting__button-wrap {
    margin-top: rem(40);

    @include mq(md) {
        margin-top: rem(37);
        margin-left: auto;
        max-width: rem(270);
    }
}

// Splideページネーションスタイル
.p-top-greeting__splide .splide__pagination {
    position: absolute;
    bottom: rem(-25);
    left: -50%;
    display: flex;
    gap: rem(4);
    z-index: 10;

    @include mq(md) {
        bottom: 0;
        transform: translateX(-50%);
        left: 36%;
        top: clamp(0rem, -24rem + 50vw, 26rem);
    }
}

.p-top-greeting__splide .splide__pagination__page {
    width: rem(50);
    height: rem(3);
    border-radius: 0;
    background-color: var(--color-white);
    border: none;
    cursor: pointer;
    opacity: 1;
    // ページネーションボタンも緩やかにアニメーション
    transition: background-color 0.3s cubic-bezier(0.25, 0.1, 0.25, 1), transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

    @include mq(md) {
        width: rem(70);
        height: rem(3);
    }

}

.p-top-greeting__splide .splide__pagination__page.is-active {
    background-color: var(--color-orange);
    transform: scale(1);
}