@use "../globals/"as *;

.c-breadcrumbs {
    color: var(--color-gray-medium-2);
}

.c-breadcrumbs__inner {
    padding-inline: var(--padding-sp);
    display: flex;
    align-items: center;
    gap: rem(5) rem(10);
    flex-wrap: wrap;
}

.c-breadcrumbs span {
    font-size: rem(12);
    font-weight: var(--medium);
    color: var(--color-gray-medium-2);
    line-height: calc(26 / 14);
    letter-spacing: 0.04em;
    text-transform: uppercase;
    flex-shrink: 1;

    @include mq(md) {
        font-size: rem(14);
    }

}

.c-breadcrumbs__inner .current-item {
    color: var(--color-orange-dark);
}