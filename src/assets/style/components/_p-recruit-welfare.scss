@use "../globals/"as *;

.p-recruit-welfare {
    padding: rem(40) rem(16);
    background-color: #f9f9f3;

    @include mq("md") {
        padding: rem(80) rem(32);
    }
}

.p-recruit-welfare__inner {
    max-width: rem(960);
    margin: 0 auto;
}

.p-recruit-welfare__heading {
    text-align: left;
    margin-bottom: rem(40);

    @include mq("md") {
        margin-bottom: rem(64);
    }
}

.p-recruit-welfare__en {
    font-size: rem(12);
    font-weight: 500;
    color: #00a651;
    letter-spacing: 0.1em;
    line-height: 1.5;
    margin-bottom: rem(4);
}

.p-recruit-welfare__ja {
    font-size: rem(28);
    font-weight: 700;
    color: #269b29;
    letter-spacing: 0.05em;
    line-height: 1.4;
}

.p-recruit-welfare__item {
    background-color: #fff;
    border-left: rem(4) solid #00a651;
    padding: rem(24);
    margin-top: rem(24);

    @include mq("md") {
        display: flex;
        align-items: flex-start;
        gap: rem(32);
        padding: rem(32);
    }
}

.p-recruit-welfare__icon {
    width: rem(24);
    height: rem(24);
    background: url("https://unsplash.it/24/24?image=999") no-repeat center / cover;
    flex-shrink: 0;
}

.p-recruit-welfare__title {
    font-size: rem(16);
    font-weight: 700;
    color: #333;
    letter-spacing: 0.05em;
    line-height: 1.6;
    margin-top: rem(8);
    margin-left: rem(8);
}

.p-recruit-welfare__content {
    margin-top: rem(16);

    @include mq("md") {
        display: flex;
        justify-content: space-between;
        margin-top: 0;
    }
}

.p-recruit-welfare__text {
    font-size: rem(14);
    font-weight: 400;
    color: #333;
    letter-spacing: 0.05em;
    line-height: 1.8;

    @include mq("md") {
        width: 60%;
    }
}

.p-recruit-welfare__img {
    margin-top: rem(16);

    @include mq("md") {
        width: 36%;
        margin-top: 0;
    }

    img {
        border-radius: rem(8);
        height: auto;
        object-fit: cover;
    }
}
