@use "../globals/"as *;

.p-privacy {
    padding-block: rem(64) rem(50);

    @include mq("md") {
        padding-block: rem(101) rem(40);
    }
}

.p-privacy__inner.l-inner {
    @include mq("md") {
        max-width: rem(950);
        margin-inline: auto;
    }
}

.p-privacy__block {
    border-bottom: 1px solid var(--color-gray-medium-2);
    padding-bottom: rem(28);

    @include mq("md") {
        padding-bottom: rem(58);
    }
}

.p-privacy__block:last-child {
    border-bottom: none;
}

.p-privacy__block+.p-privacy__block {
    margin-top: rem(35);

    @include mq("md") {
        margin-top: rem(60);
    }
}

.p-privacy__title {
    position: relative;
    font-size: rem(22);
    line-height: 1;
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    padding-bottom: rem(24);

    @include mq(md) {
        font-size: rem(34);
        line-height: calc(46 / 34);
        letter-spacing: 0.04em;
    }

}

.p-privacy__title::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: rem(32);
    height: rem(4);
    background-color: var(--color-orange-dark);

    @include mq("md") {
        width: rem(72);
    }
}

.p-privacy__text-wrap {
    margin-top: rem(24);
    display: grid;
    gap: rem(10);

    @include mq("md") {
        margin-top: rem(38);
        gap: rem(14);
    }
}


.p-privacy__text {
    margin-top: rem(25);

    @include mq("md") {
        margin-top: rem(37);
    }

}

.p-privacy__list {
    margin-top: rem(24);
    counter-reset: privacy-counter;
    display: grid;
    gap: rem(4);

    @include mq("md") {
        margin-top: rem(16);
    }
}

.p-privacy__list.p-privacy__list--top {
    @include mq("md") {
        margin-top: rem(40);
    }
}

.p-privacy__item {
    position: relative;
    padding-left: rem(21);
    counter-increment: privacy-counter;

}

.p-privacy__item::before {
    content: counter(privacy-counter) ".";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--color-orange-dark);
    font-size: rem(16);
    font-weight: var(--medium);
}