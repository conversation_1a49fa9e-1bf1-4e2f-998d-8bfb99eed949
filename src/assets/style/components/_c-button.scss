@use "../globals/"as *;

.c-button {
    padding: rem(17) rem(20);
    max-width: rem(240);
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 1.2;
    font-size: rem(16);
    font-weight: var(--semibold);
    color: var(--color-white);
    background-color: var(--color-black);
    letter-spacing: 0.04em;
    border-radius: rem(32);
    transition: opacity 0.3s ease, border-radius 0.3s ease, background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        font-size: rem(18);
        padding: rem(22) rem(25);
        max-width: rem(270);
    }
}

.c-button::after {
    content: "";
    width: rem(8);
    height: rem(8);
    clip-path: $clip-triangle-right;
    background-color: currentColor;

    @include mq("md") {
        width: rem(10);
        height: rem(10);

    }
}

@media (any-hover: hover) {
    .c-button:hover {
        opacity: 1;
        border-radius: 0;
        background-color: var(--color-orange);
    }
}

// Modifier
.c-button.c-button--white {
    color: var(--color-black);
    background-color: var(--color-white);
    transition: border-radius 0.3s ease, background-color 0.3s ease, color 0.3s ease;
}

@media (any-hover: hover) {
    .c-button.c-button--white:hover {
        background-color: var(--color-orange);
        color: var(--color-white);
    }
}

.c-button.c-button--navy {
    background-color: var(--color-blue-navy);
    color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;

    @include mq("md") {
        max-width: rem(270);
    }
}

.c-button.c-button--navy::after {
    content: none;
}

@media (any-hover: hover) {
    .c-button.c-button--navy:hover {
        background-color: var(--color-orange);
        color: var(--color-white);
    }
}