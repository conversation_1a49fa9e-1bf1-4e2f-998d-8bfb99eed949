@use "../globals/"as *;

.c-contact-button {
    padding: rem(12) rem(10);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: rem(15);
    line-height: 1.2;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: var(--color-white);
    background-color: var(--color-orange);
    border-radius: rem(32);
    gap: rem(4);
    transition: border-radius 0.3s ease, background-color 0.3s ease, color 0.3s ease;
}

@media (any-hover: hover) {
    .c-contact-button:hover {
        opacity: 1;
        border-radius: 0;
        background-color: var(--color-white);
        color: var(--color-orange);
    }
}
