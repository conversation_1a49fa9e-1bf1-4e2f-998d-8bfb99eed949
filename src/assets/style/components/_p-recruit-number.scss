@use "../globals/"as *;

.p-recruit-number {
    padding: rem(40) rem(20);
    background-color: #f8f8f8;

    @include mq("md") {
        padding: rem(80) rem(40);
    }
}

.p-recruit-number__heading {
    text-align: center;
    margin-bottom: rem(40);
}

.p-recruit-number__subtitle {
    font-size: rem(14);
    font-weight: 500;
    color: #fff;
    background-color: #000030;
    display: inline-block;
    padding: rem(4) rem(12);
    letter-spacing: 0.1em;
    line-height: 1.4;
}

.p-recruit-number__title {
    margin-top: rem(12);
    font-size: rem(28);
    font-weight: 700;
    color: #000;
    letter-spacing: 0.05em;
    line-height: 1.6;

    @include mq("md") {
        font-size: rem(36);
    }
}

.p-recruit-number__grid {
    display: grid;
    gap: rem(24);
    grid-template-columns: 1fr;

    @include mq("md") {
        grid-template-columns: repeat(3, 1fr);
        gap: rem(32);
    }
}

.p-recruit-number__card {
    background-color: #fff;
    padding: rem(24) rem(16);
    text-align: center;
    border-radius: rem(12);
    box-shadow: 0 0 rem(6) rgba(0, 0, 0, 0.05);
    position: relative;
}

.p-recruit-number__icon {
    margin-bottom: rem(16);
}

.p-recruit-number__label {
    font-size: rem(14);
    font-weight: 600;
    color: #333;
    letter-spacing: 0.05em;
    line-height: 1.5;
}

.p-recruit-number__label-en {
    font-size: rem(12);
    font-weight: 500;
    color: #1db24b;
    letter-spacing: 0.08em;
    line-height: 1.4;
    margin-top: rem(2);
}

.p-recruit-number__value {
    margin-top: rem(12);
    font-size: rem(32);
    font-weight: 700;
    color: #00b900;
    letter-spacing: 0.05em;
    line-height: 1.4;

    @include mq("md") {
        font-size: rem(36);
    }
}

.p-recruit-number__unit {
    font-size: rem(20);
    font-weight: 600;
    margin-left: rem(4);
}

.p-recruit-number__description {
    margin-top: rem(8);
    font-size: rem(12);
    font-weight: 500;
    color: #333;
    letter-spacing: 0.03em;
    line-height: 1.5;
}
