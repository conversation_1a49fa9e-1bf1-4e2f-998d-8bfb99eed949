@use "../globals/"as *;

.c-section-title-wrap {
    position: relative;
    display: grid;
    gap: rem(4);
    padding-bottom: rem(30);

    @include mq(md) {
        gap: rem(10);
    }
}

.c-section-title-wrap::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: rem(50);
    height: rem(4);
    background-color: var(--color-orange-dark);

    @include mq("md") {
        width: rem(70);
    }
}

.c-section-title-wrap.c-section-title-wrap--center::after {
    left: 50%;
    transform: translateX(-50%);
}