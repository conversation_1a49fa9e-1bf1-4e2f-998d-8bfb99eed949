@use "../globals/"as *;

.p-faq {
    padding-block: rem(63) rem(77);

    @include mq("md") {
        padding-block: rem(101) rem(100);
    }
}

.p-faq__inner.l-inner {
    @include mq("md") {
        max-width: rem(830);
        margin-inline: auto;
    }
}

.p-faq__list {
    display: grid;
    gap: rem(28);

    @include mq("md") {
        gap: rem(58);
    }
}

.p-faq__item {
    display: grid;
    gap: rem(18);

    @include mq("md") {
		gap: rem(25);
	}
}

.p-faq__question {
    position: relative;
    display: grid;
    gap: rem(15);

    @include mq("md") {
        grid-template-columns: auto 1fr;
        gap: rem(20);
    }
}

.p-faq__question-num {
    position: relative;
    font-family: var(--noto-serif-jp);
    font-size: rem(24);
    color: var(--color-orange-dark);
    font-weight: var(--regular);
    line-height: 1;
    letter-spacing: 0.04em;
    padding-bottom: rem(16);

    @include mq("md") {
        font-size: rem(36);
        padding-bottom: initial;
        padding-left: rem(23);
    }
}

.p-faq__question-num::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: rem(47);
    height: rem(2);
    background-color: var(--color-orange-dark);

    @include mq("md") {
		bottom: initial;
        top: rem(-1);
        width: rem(4);
        height: rem(42);
    }
}

.p-faq__question-text {
    font-size: rem(16);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: calc(27/16);
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(20);
        line-height: calc(36/20);
    }
}

.p-faq__answer {
    position: relative;
    display: flex;
    gap: rem(8);
    background-color: var(--color-off-white);
    padding: rem(20);

    @include mq("md") {
        padding: rem(30) rem(30) rem(28);
    }
}

.p-faq__answer-mark {
    font-family: var(--noto-serif-jp);
    font-size: rem(24);
    color: var(--color-orange-dark);
    font-weight: var(--regular);
    line-height: 1;
    letter-spacing: 0.04em;
    height: fit-content;

    @include mq("md") {
        font-size: rem(24);
    }
}

.p-faq__answer-text {
    font-size: rem(14);
    color: var(--color-dark-gray);
    font-weight: var(--medium);
    line-height: calc(26/14);
    letter-spacing: 0.04em;
	@include mq("md") {
	  padding-top: rem(2);
	}
}

.p-faq__contact {
    margin-top: rem(60);
    text-align: center;

    @include mq("md") {
        margin-top: rem(80);
    }
}

.p-faq__contact-text {
    font-size: rem(16);
    line-height: 1.8;

    @include mq("md") {
        font-size: rem(18);
    }
}

.p-faq__button-wrap {
    margin-top: rem(24);

    @include mq("md") {
        margin-top: rem(32);
    }
}