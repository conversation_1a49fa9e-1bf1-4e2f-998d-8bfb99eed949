@use "../globals/"as *;

.p-top-system {
    position: relative;
    padding-block: rem(81) rem(80);
    background: transparent linear-gradient(90deg, #1869C7 0%, #0DAC00 100%) 0% 0% no-repeat padding-box;

    @include mq(md) {
        padding-block: rem(80) rem(80);
    }
}

.p-top-system__inner {
    max-width: rem(1600);
    margin-inline: auto;
}

.p-top-system__title-wrap {
    display: grid;
    gap: rem(3);
    grid-area: title;

    @include mq("md") {
        padding-top: rem(60);
        gap: rem(10);
        grid-area: 1 / 2 / 2 / 3;
    }
}

.p-top-system__container {
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, 400px) minmax(rem(30), 1fr);
    grid-template-rows: auto auto auto;
    grid-template-areas:
        "l-margin title r-margin"
        "l-margin image r-margin"
        "l-margin text r-margin";

    @include mq("md") {
        --padding-inline: clamp(1.875rem, -27.769rem + 43.12vw, 13.625rem);
        grid-template-columns: var(--padding-inline) 643fr 57fr 400fr var(--padding-inline);
        grid-template-rows: auto auto;
    }
}

.p-top-system__image {
    margin-top: rem(25);
    grid-area: image;
    position: relative;

    @include mq(md) {
        margin-top: initial;
        grid-area: 1 / 4 / 3 / 5;
    }
}

.p-top-system__image img {
    aspect-ratio: 315 / 300;
    object-fit: cover;
    width: 100%;
    height: auto;

    @include mq("md") {
        aspect-ratio: 400 / 415;
    }
}

.p-top-system__content {
    grid-area: text;

    @include mq(md) {
        grid-area: 2 / 2 / 3 / 3;
    }
}

.p-top-system__text {
    margin-top: rem(28);
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-white);
    letter-spacing: 0.04em;
    line-height: calc(30 / 16);

    @include mq(md) {
        margin-top: rem(20);
        font-size: clamp(0.875rem, 0.56rem + 0.46vw, 1rem);
    }
}

.p-top-system__button-wrap {
    margin-top: rem(37);

    @include mq(md) {
        margin-top: rem(34);
        margin-right: auto;
        max-width: rem(270);
    }
}