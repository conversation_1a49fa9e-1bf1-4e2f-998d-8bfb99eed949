@use "../globals/"as *;

.p-recruit-faq {
    padding: rem(40) rem(20);

    @include mq("md") {
        padding: rem(80) rem(40);
    }
}

.p-recruit-faq__head {
    text-align: center;
}

.p-recruit-faq__label {
    font-size: rem(14);
    font-weight: 500;
    color: #00a000;
    letter-spacing: 0.05em;
    line-height: 1.6;
    margin-bottom: rem(10);
}

.p-recruit-faq__title img {
    margin: 0 auto;
}

.p-recruit-faq__lead {
    margin-top: rem(20);
    font-size: rem(14);
    font-weight: 400;
    color: #333;
    letter-spacing: 0.05em;
    line-height: 1.8;

    @include mq("md") {
        font-size: rem(16);
    }
}

.p-recruit-faq__list {
    margin-top: rem(40);
    display: grid;
    gap: rem(30);
}

.p-recruit-faq__item {
    background-color: #f7fdf6;
    padding: rem(20);
    border-left: rem(4) solid #00a000;

    @include mq("md") {
        padding: rem(24) rem(30);
    }
}

.p-recruit-faq__q,
.p-recruit-faq__a {
    font-size: rem(14);
    font-weight: 500;
    color: #333;
    letter-spacing: 0.05em;
    line-height: 1.8;

    @include mq("md") {
        font-size: rem(16);
    }
}

.p-recruit-faq__q-icon,
.p-recruit-faq__a-icon {
    display: inline-block;
    font-weight: bold;
    color: #00a000;
    margin-right: rem(8);
}
