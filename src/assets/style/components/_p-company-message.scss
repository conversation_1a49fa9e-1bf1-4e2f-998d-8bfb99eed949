@use "../globals/"as *;

.p-company-message {
    background-color: var(--color-blue-navy);
    padding-block: rem(60) rem(74);

    @include mq(md) {
        padding-block: rem(64) 0;
    }
}

.p-company-message__inner {
    @include mq("md") {
        max-width: rem(1600);
        margin-inline: auto;
    }
}

.p-company-message__container {
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, 500px) minmax(rem(30), 1fr);
    grid-template-rows: auto auto auto;

    @include mq(md) {
        --padding-inline: clamp(1.875rem, -27.769rem + 43.12vw, 13.625rem);
        grid-template-columns: var(--padding-inline) 560fr 40fr 500fr var(--padding-inline);
    }
}

.p-company-message__text-block {
    display: grid;
    gap: rem(18);
    grid-area: 1 / 2 / 2 / 3;

    @include mq(md) {
        grid-area: 1 / 2 / 2 / 3;
        position: relative;
        z-index: 1;
    }
}

.p-company-message__text-block::after {
    @include mq("md") {
        content: "company message";
        position: absolute;
        z-index: -1;
        left: 0;
        top: rem(120);
        color: var(--color-blue-dark);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        font-size: rem(100);
        line-height: calc(116 / 100);
        text-transform: uppercase;
        letter-spacing: -0.03em;
    }
}

.p-company-message__title {
    font-size: rem(40);
    font-weight: var(--bold);
    color: var(--color-white);
    line-height: calc(60 / 40);
    letter-spacing: 0;

    @include mq(md) {
        font-size: clamp(3.25rem, -0.219rem + 5.05vw, 4.625rem);
        line-height: calc(100 / 74);
    }

    // Safari専用のフォントサイズ調整
    @supports (-webkit-appearance: none) and (not (text-size-adjust: none)) {
        font-size: rem(32);

        @include mq("md") {
            font-size: clamp(2.5rem, -0.969rem + 5.05vw, 3.75rem);
            line-height: calc(100 / 74);
        }
    }
}

.p-company-message__description {
    grid-area: 3 / 2 / 4 / 3;
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-white);
    letter-spacing: 0.04em;
    line-height: calc(30 / 16);
    margin-top: rem(30);

    @include mq(md) {
        margin-top: initial;
        font-size: rem(16);
        padding-block: rem(54) rem(30);
        grid-area: 1 / 4 / 3 / 5;
    }
}

.p-company-message__image {
    position: relative;
    z-index: 2;
    margin-top: rem(28);
    grid-area: 2 / 1 / 3 / 3;

    @include mq(md) {
        margin-top: rem(36);
        padding-right: rem(38);
        grid-area: 2 / 1 / 4 / 3;
    }
}

.p-company-message__image img {
    aspect-ratio: 345/136;
    width: 100%;
    height: auto;
    object-fit: cover;

    @include mq(md) {
        aspect-ratio: 740/316;
    }
}

.p-company-message__bg {
    display: none;

    @include mq("md") {
        display: block;
        grid-area: 3 / 1 / 4 / 6;
        border-image: linear-gradient(var(--color-white) 0 0) fill 0 / /0 100vi;
        height: rem(158);
    }
}