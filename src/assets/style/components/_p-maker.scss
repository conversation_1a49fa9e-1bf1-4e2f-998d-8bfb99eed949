@use "../globals/"as *;

.p-maker {
    background-color: var(--color-white);
    padding-block: rem(60) rem(81);

    @include mq(md) {
        padding-block: rem(102) rem(100);
    }
}

.p-maker__container {
    width: 100%;
}

/* 検索フォーム */
.p-maker__search {
    background-color: var(--color-off-white);
    padding: rem(15) rem(20) rem(20);

    @include mq("md") {
        padding: rem(59) rem(20);
    }
}

.p-maker__search-form {
    display: grid;
    gap: rem(16);

    @include mq("md") {
        width: min(100%, rem(900));
        margin-inline: auto;
        grid-template-columns: 440fr 365fr;
        gap: rem(95);
    }
}

.p-maker__search-item {
    display: grid;
    gap: rem(10);

    @include mq("md") {
        grid-template-columns: auto 1fr;
        gap: rem(20);
    }
}

.p-maker__input-group {
    position: relative;
}

.p-maker__search-label {
    position: relative;
    display: flex;
    align-items: center;
    gap: rem(15);
    font-size: rem(16);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: calc(34 / 16);

    @include mq("md") {
        font-size: rem(16);
        gap: rem(14);
    }
}

.p-maker__search-label::before {
    content: "";
    display: inline-flex;
    width: rem(4);
    height: rem(20);
    background-color: var(--color-gray-darker-2);
}

//リセットCSSの詳細度に負けるため.p-maker__input-groupを追加
.p-maker__input-group .p-maker__search-input {
    width: 100%;
    padding: rem(15.5) rem(50) rem(15.5) rem(16);
    border-radius: rem(30);
    font-size: rem(14);
    font-weight: var(--bold);
    color: var(--color-gray-darker-2);
    background-color: var(--color-white);

    @include mq("md") {
        padding: rem(15) rem(60) rem(15) rem(20);
    }
}

.p-maker__search-input::placeholder {
    color: var(--color-gray-darker-2);
}

.p-maker__search-input:focus {
    outline: none;
    border-color: var(--color-blue);
}

/* オートコンプリート時の背景色を制御 */
.p-maker__search-input:-webkit-autofill,
.p-maker__search-input:-webkit-autofill:hover,
.p-maker__search-input:-webkit-autofill:focus,
.p-maker__search-input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 1000px var(--color-white) inset !important;
    -webkit-text-fill-color: var(--color-gray-darker-2) !important;
    transition: background-color 5000s ease-in-out 0s;
}

.p-maker__search-button {
    position: absolute;
    right: rem(3);
    top: 50%;
    transform: translateY(-50%);
    width: rem(46);
    height: rem(46);
    background-color: var(--color-gray-medium-2);
    border-radius: 50%;
    cursor: pointer;
    color: var(--color-white);
    transition: background-color 0.3s ease;

}

.p-maker__search-button:hover {
    background-color: var(--color-dark-gray);
}

.p-maker__search-icon {
    display: block;
    width: rem(16);
    height: rem(16);
    margin: auto;
    background-color: var(--color-white);

    @include mq("md") {
        width: rem(18);
        height: rem(18);
    }
}

.p-maker__select-wrapp {
    position: relative;
}


.p-maker__search-select {
    width: 100%;
    padding: rem(15.5) rem(40) rem(15.5) rem(16);
    border-radius: rem(30);
    font-size: rem(14);
    font-weight: var(--bold);
    color: var(--color-gray-darker-2);
    background-color: var(--color-white);
    appearance: none;

    @include mq("md") {
        padding: rem(15) rem(50) rem(15) rem(20);
    }

}

.p-maker__search-select:focus {
    outline: none;
    border-color: var(--color-blue);
}

.p-maker__select-icon {
    position: absolute;
    right: rem(12);
    top: 50%;
    transform: translateY(-50%);
    width: rem(20);
    color: var(--color-gray-darker-2);
}

/* メインコンテンツ */
.p-maker__content {
    margin-top: rem(58);

    @include mq("md") {
        margin-top: rem(100);
    }
}

/* カテゴリ別一覧 */
.p-maker__categories {
    display: grid;
    gap: rem(79);

    @include mq("md") {
        gap: rem(100);
    }
}

.p-maker__category {
    position: relative;

    @include mq("md") {
        display: grid;
        gap: rem(30);
        grid-template-columns: 180fr 890fr;
    }
}

.p-maker__category-title {
    position: relative;
    font-size: rem(22);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: 1.5;
    padding-bottom: rem(20);

    @include mq("md") {
        font-size: rem(30);
        padding-bottom: rem(26);
        height: fit-content;
    }

    // PC時のみbrタグを表示
    br {
        display: none;

        @include mq("md") {
            display: block;
        }
    }
}

.p-maker__category-title::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: rem(32);
    height: rem(4);
    background-color: var(--color-orange-dark);

    @include mq("md") {
        width: rem(72);
        height: rem(4);
    }
}

.p-maker__category-title-text {
    display: block;
}

/* メーカーグリッド */
.p-maker__list {
    margin-top: rem(30);
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: rem(5);

    @include mq("md") {
        margin-top: initial;
        grid-template-columns: repeat(3, 1fr);
        gap: rem(20) rem(20);
    }
}

.p-maker__item {
        background-color: var(--color-off-white);
        font-size: rem(14);
        font-weight: var(--bold);
        color: var(--color-gray-dark);
        line-height: calc(21 / 14);
        text-align: center;
        align-content: center;

        @include mq("md") {
            font-size: rem(16);
        }
}


.p-maker__item a {
    display: block;
    padding: rem(9.5) rem(10);
    background-color: var(--color-off-white);
    font-size: rem(14);
    font-weight: var(--bold);
    color: var(--color-gray-dark);
    line-height: calc(21 / 14);
    text-align: center;
    transition: background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        padding: rem(10) rem(4);
        font-size: rem(16);
    }
}

.p-maker__item a:hover {
    background-color: var(--color-dark-gray);
    color: var(--color-white);
}

/* 検索結果 */
.p-maker__search-results {
    text-align: center;
    padding: rem(40) rem(20);

    @include mq("md") {
        padding: rem(60) rem(40);
    }
}

.p-maker__search-results-title {
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-dark-gray);

    @include mq("md") {
        font-size: rem(22);
    }
}