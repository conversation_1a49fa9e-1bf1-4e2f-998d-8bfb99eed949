@use "../globals/"as *;

.p-company {
    padding-block: rem(62) rem(82);
    background-color: var(--color-white);

    @include mq("md") {
        padding-block: rem(100) rem(160);
    }
}

.p-company__inner.l-inner {
    @include mq("md") {
        max-width: rem(950);
        margin-inline: auto;
    }
}

.p-company__content {
    background-color: var(--color-off-white);
    padding: rem(27) rem(20);

    @include mq("md") {
        padding: rem(49) rem(60) rem(81);
    }
}

.p-company__list {
    display: grid;
    gap: rem(22);

    @include mq("md") {
        gap: rem(0);
    }
}

.p-company__item {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--color-gray-medium-2);
    gap: rem(5);
    padding-bottom: rem(20);

    @include mq(md) {
        flex-direction: row;
        justify-content: space-between;
        gap: rem(35);
        padding-block: rem(27.5);
    }
}


.p-company__term {
    font-size: rem(16);
    font-weight: var(--bold);
    color: var(--color-orange-dark);
    letter-spacing: 0.04em;
    line-height: calc(30 / 16);

    @include mq(md) {
        font-size: rem(18);
        line-height: calc(30 / 18);
        min-width: rem(160);
    }
}


.p-company__description,
.p-company__description-item {
    font-size: rem(16);
    font-weight: var(--medium);
    letter-spacing: 0.04em;
    line-height: calc(30 / 16);
    color: var(--color-dark-gray);


    @include mq(md) {
        width: 100%;
    }
}

.p-company__description-item:not(:first-child) {
    margin-top: rem(10);

    @include mq(md) {
        margin-top: rem(15);
    }
}