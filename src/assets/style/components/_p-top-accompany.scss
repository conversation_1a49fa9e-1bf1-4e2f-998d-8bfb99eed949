@use "../globals/"as *;

.p-top-accompany {
    display: grid;
    grid-template-columns: var(--padding-sp) 1fr var(--padding-sp);

    @include mq(md) {
        --padding-outer: clamp(0rem, -66.912rem + 76.47vw, 6.5rem);
        --padding-inner: clamp(1.875rem, -52.169rem + 61.76vw, 7.125rem);
        --inner: 1fr;
        display: grid;
        grid-template-columns: var(--padding-outer) var(--padding-inner) var(--inner) var(--padding-inner) var(--padding-outer);
    }
}

.p-top-accompany__bg {
    position: relative;
    background-image: image-set(url('/images/bg_accompany_pc.webp') type('image/webp'),
            url('/images/bg_accompany_pc.jpg') type('image/jpg'));
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-top-right-radius: rem(100);
    grid-area: 1 / 1 / 2 / 4;

    @include mq(md) {
        background-image: image-set(url('/images/bg_accompany_pc.webp') type('image/webp'),
                url('/images/bg_accompany_pc.jpg') type('image/jpg'));
        grid-area: 1 / 1 / 2 / 5;
        border-top-right-radius: rem(200);
    }
}

.p-top-accompany__inner {
    padding-block: rem(81) rem(80);
    width: min(100%, rem(500));
    margin-inline: auto;
    grid-area: 1 / 2 / 2 / 3;

    @include mq("md") {
        position: relative;
        z-index: 1;
        grid-area: 1 / 3 / 2 / 4;
        width: min(100%, rem(1100));
        padding-block: rem(100) rem(120);
    }
}

.p-top-accompany__inner::after {
    @include mq("md") {
        content: "about\A accompaniment";
        position: absolute;
        top: 0;
        right: calc(50% - min(48vw, rem(720)));
        color: var(--color-off-white);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        text-transform: uppercase;
        font-size: clamp(4.375rem, -0.356rem + 6.88vw, 6.25rem);
        line-height: calc(112 / 100);
        letter-spacing: -0.03em;
        z-index: -1;
        text-align: right;
        white-space: pre-line;
    }
}

.p-top-accompany__content {
    position: relative;
}

.p-top-accompany__title-wrap {
    @include mq("md") {
        display: grid;
        gap: rem(10);
    }
}

.p-top-accompany__text {
    margin-top: rem(24);
}

.p-top-accompany__schedule {
    margin-top: rem(27);
    position: relative;
    background-color: var(--color-white);
    padding-block: rem(25) rem(31);

    @include mq(md) {
        margin-top: rem(50);
        padding-block: rem(32);
    }
}

.p-top-accompany__schedule-header {
    display: flex;
    gap: rem(16);
    align-items: center;
}

.p-top-accompany__schedule-header::before {
    content: "";
    width: rem(10);
    height: rem(55);
    background-color: var(--color-blue-navy);
    display: inline-block;

    @include mq("md") {
        width: rem(16);
        height: rem(40);
    }
}

.p-top-accompany__schedule-title-wrap {
    display: flex;
    flex-direction: column;

    @include mq(md) {
        flex-direction: row;
        align-items: flex-end;
    }
}

.p-top-accompany__schedule-title {
    position: relative;
    display: inline-flex;
    align-items: flex-end;
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: 1.5;
    letter-spacing: 0.04em;

    @include mq(md) {
        font-size: rem(24);
    }
}


.p-top-accompany__schedule-month {
    margin-right: rem(4);
    font-size: rem(42);
    font-weight: var(--semibold);
    color: var(--color-dark-gray);
    line-height: 1;
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(50);
    }
}

.p-top-accompany__schedule-date {
    margin-top: rem(2);
    padding-left: rem(5);
    font-size: rem(12);
    font-weight: var(--semibold);
    color: var(--color-dark-gray);
    letter-spacing: 0.04em;
    line-height: 1.5;

    @include mq(md) {
        margin-top: initial;
        padding-left: initial;
        padding-bottom: rem(3);
        margin-left: rem(8);
        font-size: rem(16);
    }
}

.p-top-accompany__schedule-list {
    margin-top: rem(15);
    display: grid;
    grid-template-columns: 1fr;
    gap: rem(8);
    padding-inline: rem(30);

    @include mq(md) {
        margin-top: rem(16);
        grid-template-columns: repeat(4, 1fr);
        column-gap: rem(20);
        row-gap: 0;
        padding-inline: rem(40);
    }
}

.p-top-accompany__schedule-item-list {
    display: grid;
    gap: rem(5);
    padding-block: rem(12);
    border-bottom: 1px solid var(--color-gray-medium-2);

    @include mq("md") {
        padding-block: rem(20);
    }
}

.p-top-accompany__schedule-item-list:last-child {
    border-bottom: none;
    @include mq("md") {
        border-bottom: 1px solid var(--color-gray-medium-2);
    }
}

.p-top-accompany__schedule-item-list.p-top-accompany__schedule-item-list--last-row {
    @include mq("md") {
      border-bottom: none;
    }
}

.p-top-accompany__schedule-item {
    position: relative;
    font-size: rem(14);
    font-weight: var(--medium);
    color: var(--color-dark-gray);
    line-height: 1.5;
    padding-left: 1em;

    @include mq("md") {
        font-size: rem(16);
    }
}

.p-top-accompany__schedule-item::before {
    content: "•";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--color-gray-dark);
    font-size: rem(18);
    line-height: 1;

    @include mq("md") {
        font-size: rem(18);
        top: 0.1em;
    }
}