@use "../globals/"as *;

.p-news {
    background-color: var(--color-white);
    padding-block: rem(62) rem(80);
}

@include mq(md) {
    .p-news {
        padding-block: rem(100) rem(100);
    }
}

.p-news__inner.l-inner {
    @include mq("md") {
        max-width: rem(950);
        width: 100%;
        margin-inline: auto;
    }
}

.p-news__container {
    width: 100%;
    background-color: var(--color-off-white);
    padding-bottom: rem(30);

    @include mq("md") {
        padding-bottom: rem(40);
    }
}

.p-news__tab-list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
}

.p-news__tab-item {}

.p-news__tab-item a {
    position: relative;
    display: block;
    height: 100%;
    text-align: center;
    align-content: center;
    padding: rem(14) rem(4);
    transition: background-color 0.3s ease, color 0.3s ease;
    font-size: rem(14);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: calc(18 / 14);
    letter-spacing: 0.04em;

    @include mq("md") {
        padding: rem(20.5) rem(4);
        font-size: rem(20);
        line-height: calc(28 / 20);
    }
}

.p-news__tab-item a:hover {
    opacity: 1;
    background-color: var(--color-blue);
    color: var(--color-white);
}

.p-news__tab-item--active a {
    background-color: var(--color-blue);
    color: var(--color-white);
    position: relative;
    z-index: 1;
}

.p-news__tab-item:nth-child(n + 2) a::before {
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: -1px;
    right: 0;
    width: rem(2);
    height: 78.5%;
    background-color: var(--color-gray-medium-2);
}

/* コンテンツエリア */
.p-news__content {
    margin-top: rem(31);
    display: grid;
    gap: rem(12);

    @include mq("md") {
        padding-inline: rem(37);
        margin-top: rem(50);
        grid-template-columns: 190fr 540fr;
        gap: rem(80);
    }
}

.p-news__content.p-news__content--accompany {
    @include mq("md") {
        grid-template-columns: 200fr 500fr;
        gap: rem(53);
    }
}

/* サイドバー */
.p-news__sidebar {

    @include mq("md") {
        padding-top: rem(17);
    }
}

.p-news__sidebar.p-news__sidebar--hotnews {
    @include mq("md") {
        padding-top: rem(9);
    }
}

.p-news__sidebar.p-news__sidebar--accompany {
    @include mq("md") {
        padding-top: rem(15);
    }
}

.p-news__filter {
    padding-inline: rem(20);
    border-radius: rem(8);

    @include mq("md") {
        padding-inline: initial;
    }
}

.p-news__filter-title {
    position: relative;
    padding-bottom: rem(16);
    text-align: center;
    font-size: rem(22);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: 1;

    @include mq("md") {
        padding-bottom: initial;
        font-size: rem(30);
        text-align: left;
        padding-left: rem(34);

    }
}

.p-news__filter-title.p-news__filter-title--hotnews {
    @include mq("md") {
        font-size: rem(30);
        line-height: calc(42 / 30);
    }
}

.p-news__filter-title::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: rem(32);
    height: rem(4);
    background-color: var(--color-gray-darker-2);

    @include mq("md") {
        left: rem(8);
        top: 60%;
        transform: translateY(-50%);
        width: rem(4);
        height: rem(28);
    }
}

.p-news__filter-title.p-news__filter-title--hotnews::before {
    @include mq("md") {
        left: rem(8);
        top: 54%;
        transform: translateY(-50%);
        width: rem(4);
        height: rem(66);
    }
}

.p-news__filter-title.p-news__filter-title--accompany::before {
    @include mq("md") {
        left: rem(8);
        top: 54%;
        transform: translateY(-50%);
        width: rem(4);
        height: rem(111);
    }
}

.p-news__filter-title-text {
    display: inline-flex;
    align-items: flex-end;
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: 1.5;
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(24);
        line-height: calc(42 / 24);
    }
}

.p-news__filter-title-text span {
    font-size: rem(42);
    font-weight: var(--semibold);
    color: var(--color-dark-gray);
    line-height: 1;
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(50);
    }
}

.p-news__filter-title-text-sub {
    display: block;
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-dark-gray);
    line-height: 1.5;
    letter-spacing: 0.04em;

    @include mq("md") {
        margin-top: rem(-6);
        font-size: rem(24);
        line-height: calc(42 / 24);
    }
}

.p-news__filter-title-time {
    margin-top: rem(7);
    padding-left: rem(2);
    display: block;
    font-size: rem(12);
    font-weight: var(--medium);
    color: var(--color-dark-gray);
    line-height: calc(24 / 12);
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(16);
        line-height: calc(24 / 16);
    }
}

.p-news__filter-list {
    margin-top: rem(20);
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: rem(4);

    @include mq("md") {
        margin-top: rem(36);
        margin-left: rem(8);
        display: flex;
        flex-direction: column;
        gap: rem(10);
        max-width: rem(190);
    }
}

.p-news__filter-item {
    @include mq("md") {
        margin-right: rem(-4);
    }
}

.p-news__filter-item a {
    display: block;
    text-align: center;
    padding: rem(9) rem(4);
    background-color: var(--color-gray-darker-2);
    font-size: rem(14);
    font-weight: var(--medium);
    color: var(--color-white);
    line-height: calc(18 / 14);
    letter-spacing: 0.04em;
    transition: background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        padding: rem(10) rem(20);
        font-size: rem(16);

    }
}

.p-news__filter-item:nth-child(1) a {
    border-radius: rem(30) rem(0) rem(0) rem(30);
}

.p-news__filter-item:nth-child(3) a {
    border-radius: rem(0) rem(30) rem(30) rem(0);
}

.p-news__filter-item:nth-child(n+1) a {
    @include mq("md") {
        border-radius: rem(30);
    }
}

.p-news__filter-item a:hover {
    background-color: var(--color-blue-navy);
    opacity: 1;
    color: var(--color-white);
}

.p-news__filter-item a.is-active {
    background-color: var(--color-blue-navy);
}

/* メインコンテンツ */
.p-news__main {
    padding-inline: rem(20);
    order: 1;

    @include mq("md") {
        margin-top: rem(-21);
        padding-inline: initial;
        padding-left: rem(4);
        padding-right: rem(7);
    }
}

/* 記事リスト */

.p-news__item a {
    display: grid;
    gap: rem(4);
    padding: rem(18) 0;
    transition: background-color 0.3s ease;
    border-bottom: 1px solid var(--color-gray-medium-2);

    @include mq("md") {
        grid-template-columns: auto auto;
        padding: rem(28) 0 rem(16);
        gap: rem(27);
    }
}

.p-news__item a:hover {
    opacity: 1;
}

.p-news__item a:hover .p-news__title {

    @include mq("md") {
        text-decoration: underline;
        text-underline-offset: rem(4);
        text-decoration-thickness: 1px;
        text-decoration-color: var(--color-blue);
        color: var(--color-blue);
    }
}

.p-news__meta {
    display: flex;
    align-items: center;
    gap: rem(10);

    @include mq("md") {
        align-items: flex-start;
        gap: rem(9);
    }
}

.p-news__category {
    display: inline-block;
    padding: rem(1.5) rem(12.5);
    background-color: var(--color-gray-medium);
    font-size: rem(12);
    color: var(--color-white);
    font-weight: var(--medium);
    line-height: calc(18 / 12);
    border-radius: rem(2);
    text-align: center;

    @include mq("md") {
        font-size: rem(12);
        padding: rem(2) rem(5);
        min-width: rem(57);
    }
}

.p-news__date {
    font-size: rem(16);
    font-family: var(--noto-serif-jp);
    color: var(--color-gray-medium);
    font-weight: var(--regular);
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(16);
    }
}

.p-news__title {
    font-size: rem(16);
    font-weight: var(--medium);
    color: var(--color-dark-gray);
    line-height: calc(26 / 16);
    letter-spacing: 0.04em;
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;

    @include mq("md") {
        position: relative;
        padding-right: rem(42);
        transition: color 0.3s ease-in-out;
    }
}

.p-news__title::after {
    @include mq("md") {
        content: "";
        position: absolute;
        right: 0;
        top: rem(10);
        background-color: var(--color-gray-medium-2);
        clip-path: $clip-triangle-right;
        width: rem(10);
        height: rem(10);
    }
}

/* ページネーション */
.p-news__pagination {
    margin-top: rem(60);
    text-align: center;

    @include mq("md") {
        margin-top: rem(80);
    }
}

.p-news__pagination-nav {
    margin-top: rem(30);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: rem(5);

    @include mq("md") {
        margin-top: rem(57);
        gap: rem(10);
    }
}

/* WordPressのpaginate_links()が生成するページ番号リンク */
.p-news__pagination-nav a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: rem(30);
    height: rem(30);
    background-color: var(--color-white);
    font-size: rem(12);
    color: var(--color-gray-darker-2);
    font-weight: var(--medium);
    line-height: 1;
    border-radius: 50%;
    transition: background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        width: rem(40);
        height: rem(40);
        font-size: rem(16);
    }
}

.p-news__pagination-nav a:hover {
    background-color: var(--color-blue-navy);
    color: var(--color-white);
}

/* 現在のページ（WordPressのpaginate_links()が生成するspan.current） */
.p-news__pagination-nav .current {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-blue-navy);
    color: var(--color-white);
    border-radius: 50%;
    width: rem(30);
    height: rem(30);

    @include mq("md") {
        width: rem(40);
        height: rem(40);
    }
}

/* 省略記号（WordPressのpaginate_links()が生成するspan.dots） */
.p-news__pagination-nav .dots {
    padding-bottom: rem(8);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: rem(30);
    height: rem(30);
    background-color: var(--color-white);
    border-radius: 50%;
    color: var(--color-gray-darker-2);
    font-size: rem(12);
    font-weight: var(--medium);
    line-height: 1;
    transition: background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        width: rem(40);
        height: rem(40);
        font-size: rem(16);
    }
}

/* 前後の矢印ボタン */
//詳細度を上げるためにclassを使用
.p-news__pagination-prev[class],
.p-news__pagination-next[class] {
    font-size: rem(8);

    @include mq("md") {
        font-size: rem(12);
    }
}

/* 投稿なしの場合 */
.p-news__no-posts {
    text-align: center;
    padding: rem(60) rem(20);

    @include mq("md") {
        padding: rem(80) rem(40);
    }
}

.p-news__no-posts p {
    font-size: rem(16);
    color: var(--color-dark-gray);

    @include mq("md") {
        font-size: rem(18);
    }
}

// 同行情報ページのスタイル
.p-news__accompany-schedule {
    padding-block: rem(10) rem(10);

    @include mq(md) {
        padding-block: rem(10) rem(6);
    }
}

.p-news__accompany-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: rem(8);

    @include mq(md) {
        grid-template-columns: repeat(2, 1fr);
        column-gap: rem(20);
        row-gap: 0;
        max-width: rem(500);
    }
}

.p-news__accompany-item-list {
    display: grid;
    gap: rem(5);
    padding-block: rem(12);
    border-bottom: 1px solid var(--color-gray-medium-2);

    @include mq("md") {
        padding-block: rem(19.5);
    }
}

.p-news__accompany-item-list:last-child {
    border-bottom: none;

    @include mq("md") {
        border-bottom: 1px solid var(--color-gray-medium-2);
    }
}

.p-news__accompany-item-list.p-news__accompany-item-list--last-row {
    @include mq("md") {
        border-bottom: none;
    }
}

.p-news__accompany-item {
    position: relative;
    font-size: rem(14);
    font-weight: var(--medium);
    color: var(--color-dark-gray);
    line-height: 1.5;
    padding-left: 1em;
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(16);
        padding-left: rem(10);
    }
}

.p-news__accompany-item::before {
    content: "•";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--color-gray-dark);
    font-size: rem(18);
    line-height: 1;

    @include mq("md") {
        font-size: rem(18);
        top: 0.1em;
    }
}