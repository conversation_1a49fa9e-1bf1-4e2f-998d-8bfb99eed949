@use "../globals/"as *;

.p-contact {
    padding-block: rem(60) rem(82);

    @include mq("md") {
        padding-block: rem(99) rem(100);
    }
}

.p-contact__inner.l-inner {
    @include mq("md") {
        max-width: rem(950);
        margin-inline: auto;
    }
}


.p-contact__text {
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;
    color: var(--color-dark-gray);
    text-align: center;

    @include mq("md") {
        font-size: rem(14);
    }
}

.p-contact__content {
    margin-top: rem(25);
    background-color: var(--color-off-white);
    padding: rem(30) rem(20);

    @include mq("md") {
        margin-top: rem(37);
        padding: rem(60) rem(60);
    }
}

.p-contact__form {
    display: flex;
    flex-direction: column;
    gap: rem(22);

    @include mq("md") {
        gap: rem(61);
    }
}

.p-contact__field {
    display: flex;
    flex-direction: column;
    gap: rem(10);
    padding-bottom: rem(22);
    border-bottom: 1px solid var(--color-gray-medium-2);

    @include mq("md") {
        flex-direction: row;
        gap: rem(12);
        padding-bottom: rem(56);
    }
}


.p-contact__zip-container {
    display: flex;
    flex-direction: column;
    gap: rem(10);

    @include mq("md") {
        flex-direction: row;
        gap: rem(12);
    }
}

.p-contact__field.p-contact__field--textarea {
    padding-bottom: rem(20);

    @include mq("md") {
        align-items: flex-start;
        padding-bottom: rem(55);
    }
}

.p-contact__field.p-contact__field--textarea .p-contact__label {
    @include mq("md") {
        padding-top: rem(12);
    }
}

/* バリデーションエラー時のスタイル */
.p-contact__field .wpcf7-not-valid-tip {
    color: var(--color-red-dark);
    font-weight: var(--medium);
    font-size: rem(14);
    line-height: calc(20 / 14);
    letter-spacing: 0.04em;
    margin-top: rem(3);
}

/* バリデーションエラー時のスタイル */
.p-contact__field .wpcf7-not-valid {
    border: 1px solid var(--color-red-dark) !important;
}

.p-contact__field .wpcf7-form-control-wrap {
    @include mq("md") {
        width: 100%;
    }
}

.p-contact__field.p-contact__field--tel .wpcf7-form-control-wrap {
    @include mq("md") {
        width: 100%;
        max-width: rem(290);
    }
}

.p-contact__label {
    display: flex;
    align-items: center;
    gap: rem(10);
    pointer-events: none;

    @include mq("md") {
        min-width: rem(180);
        gap: rem(10);
    }
}


.p-contact__label-text {
    font-size: rem(16);
    font-weight: var(--bold);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;
    color: var(--color-dark-gray);
}

.p-contact__required {
    padding: 0 rem(5);
    font-size: rem(12);
    font-weight: var(--medium);
    line-height: calc(20 / 12);
    color: var(--color-white);
    background-color: var(--color-orange-dark);
}

.p-contact__optional {
    padding: 0 rem(5);
    font-size: rem(12);
    font-weight: var(--medium);
    line-height: calc(20 / 12);
    color: var(--color-white);
    background-color: var(--color-gray-dark);
}


.p-contact__field .p-contact__input,
.p-contact__field .p-contact__textarea {
    width: 100%;
    padding: rem(10) rem(20);
    font-size: rem(14);
    background-color: var(--color-white);
    color: var(--color-dark-gray);
    font-weight: var(--medium);
    line-height: calc(30 / 14);
    letter-spacing: 0.04em;
    border: 1px solid transparent;

    @include mq("md") {
        padding: rem(13) rem(20);
    }
}

.p-contact__input::placeholder,
.p-contact__textarea::placeholder {
    color: var(--color-gray-medium-2);
}


/* オートコンプリート時の背景色を制御 */
.p-contact__input:-webkit-autofill,
.p-contact__input:-webkit-autofill:hover,
.p-contact__input:-webkit-autofill:focus,
.p-contact__input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 1000px var(--color-white) inset !important;
    transition: background-color 5000s ease-in-out 0s;
}

.p-contact__textarea {
    min-height: rem(240);

    @include mq("md") {
        min-height: rem(210);
    }
}

.p-contact__zip-wrap {
    display: flex;
    flex-direction: column;
    gap: rem(10);

    @include mq("md") {
        display: grid;
        grid-template-columns: 290fr 190fr;
        grid-template-rows: repeat(2, auto);
        grid-column-gap: rem(10);
        max-width: rem(490);
        width: 100%;
    }
}

.p-contact__zip-wrap .wpcf7-form-control-wrap {
    @include mq("md") {
        grid-area: 1 / 1 / 2 / 2;
    }
}

.p-contact__input+.wpcf7-not-valid-tip {
    order: 1;
}

.p-contact__zip-wrap .wpcf7-form-control-wrap {
    display: contents;

    @include mq("md") {
        display: block;
    }
}


.p-contact__zip-button {
    padding: rem(10) rem(18);
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;
    color: var(--color-white);
    background-color: var(--color-dark-gray);
    transition: background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        flex-shrink: 0;
        max-width: rem(190);
        max-height: rem(56);
        width: 100%;
        grid-area: 1 / 2 / 2 / 3;
    }
}

@media (any-hover: hover) {
    .p-contact__zip-button:hover {
        background-color: var(--color-orange-dark);
        color: var(--color-white);
    }
}

.p-contact__privacy-policy {
    margin-top: rem(17);
    text-align: center;

    @include mq("md") {
        margin-top: rem(54);
    }
}

.p-contact__privacy-link {
    font-size: rem(14);
    font-weight: var(--medium);
    line-height: calc(30 / 14);
    letter-spacing: 0.04em;
    color: var(--color-gray-darker-2);
    text-decoration: underline;
    text-underline-offset: rem(2);
    transition: color 0.3s ease;

}

@media (any-hover: hover) {
    .p-contact__privacy-link:hover {
        color: var(--color-dark-gray);
        text-decoration: underline;
    }
}


.p-contact__agreement {
    margin-top: rem(9);
    text-align: center;

    @include mq("md") {
        margin-top: rem(12);
    }
}

.p-contact__agreement .wpcf7-list-item label {
    position: relative;
    display: flex;
    align-items: center;
    padding-left: rem(60);
    cursor: pointer;
    transition: opacity 0.3s ease;

    @include mq("md") {
        padding-left: rem(32);
    }
}

@media (any-hover: hover) {
    .p-contact__agreement .wpcf7-list-item label:hover {
        opacity: 0.7;
    }
}

.p-contact__agreement .wpcf7-list-item .wpcf7-list-item-label {
    font-size: rem(14);
    font-weight: var(--medium);
    line-height: calc(21 / 14);
    letter-spacing: 0.04em;
    color: var(--color-black-2);
    text-align: left;
}


.p-contact__checkbox {
    position: absolute;
    top: rem(2);
    left: rem(30);
    appearance: none;
    -webkit-appearance: none;
    width: rem(18);
    height: rem(18);
    border-radius: 50%;
    background-color: var(--color-gray-medium-2);
    cursor: pointer;

    @include mq("md") {
        top: rem(3);
        left: rem(2);
        width: rem(20);
        height: rem(20);
    }
}

.p-contact__checkbox:checked {
    border-color: var(--color-orange-dark);
}

.p-contact__checkbox:checked::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: rem(12);
    height: rem(5);
    border: rem(2) solid var(--color-orange-dark);
    border-width: 0 0 rem(2) rem(2);
    transform: translate(-50%, -60%) rotate(-45deg);

    @include mq("md") {
        width: rem(13);
        height: rem(6);
    }
}

.p-contact__submit-wrap {
    margin-top: rem(31);
    text-align: center;
    max-width: rem(240);
    margin-inline: auto;

    @include mq("md") {
        margin-top: rem(40);
        max-width: rem(270);
    }
}

.p-contact__submit {
    width: 100%;
    padding: rem(17) rem(20);
    font-size: rem(16);
    font-weight: var(--semibold);
    line-height: 1.2;
    color: var(--color-white);
    background-color: var(--color-black);
    letter-spacing: 0.04em;
    border-radius: rem(32);
    transition: opacity 0.3s ease, border-radius 0.3s ease, background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        padding: rem(15) rem(20);
        font-size: rem(18);
        line-height: calc(34 / 18);
    }
}

@media (any-hover: hover) {
    .p-contact__submit:hover {
        opacity: 1;
        border-radius: 0;
        background-color: var(--color-orange);
    }
}

.wpcf7-spinner {
    display: none;
}

.wpcf7-list-item {
    margin: 0;
}

.p-contact__error-message {
    color: var(--color-red-dark);
    font-weight: var(--medium);
    font-size: rem(14);
    line-height: calc(20 / 14);
    letter-spacing: 0.04em;
    margin-top: rem(5);
    width: 100%;
    display: block;

    @include mq("md") {
        display: grid;
        grid-area: 2 / 1 / 3 / 3;
    }
}