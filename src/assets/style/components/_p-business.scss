@use "../globals/"as *;

.p-business {
    position: relative;
    padding-block: 0 rem(76);

    @include mq(md) {
        padding-block: 0 rem(157);
    }
}

.p-business__inner {
    position: relative;
    z-index: 1;
    margin-inline: auto;

    @include mq("md") {
        max-width: rem(1563);
    }
}

.p-business__inner::after {
    @include mq("md") {
        content: "business";
        position: absolute;
        bottom: rem(-44);
        left: calc(50% - min(48vw, rem(680)));
        color: var(--color-off-white);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        text-transform: uppercase;
        font-size: rem(136);
        line-height: calc(128 / 136);
        letter-spacing: 0.01em;
        z-index: -1;
        text-align: right;
        white-space: pre-line;
    }
}

.p-business__container {
    display: grid;
    grid-template-columns: minmax(rem(30), 1fr) minmax(auto, rem(500)) minmax(rem(30), 1fr);
    grid-template-rows: repeat(4, auto);

    @include mq("md") {
        margin-top: rem(36);
        --padding-inline: clamp(5.3125rem, -40.089rem + 55.95vw, 13.625rem);
        --grid-gap: clamp(1.25rem, -11.365rem + 18.35vw, 6.25rem);
        grid-template-columns: var(--padding-inline) var(--grid-gap) 360fr 40fr 500fr var(--grid-gap) var(--padding-inline);
        grid-template-rows: repeat(3, auto);

    }
}


.p-business__title-wrap {
    margin-top: rem(30);
    grid-area: 4 / 2 / 5 / 3;

    @include mq("md") {
        margin-top: rem(62);
        grid-area: 3 / 3 / 4 / 4;

        .c-section-title {
            letter-spacing: 0.06em;
        }
    }
}

.p-business__title-wrap.c-section-title-wrap {
    @include mq("md") {
        padding-bottom: rem(40);
    }
}

.p-business__content {
    margin-top: rem(26);
    grid-area: 5 / 2 / 6 / 3;
    display: grid;
    gap: rem(2);

    @include mq(md) {
        margin-top: rem(59);
        gap: 0;
        grid-area: 3 / 5 / 5 / 6;
    }
}

.p-business__message {
    margin-top: rem(-2);
    text-align: right;

}