@use "../globals/"as *;

.c-section-title {
    position: relative;
    z-index: 10;
    font-size: rem(22);
    font-weight: var(--bold);
    font-family: var(--noto-sans-jp);
    color: var(--color-white);
    line-height: 1.5;
    text-align: center;

    @include mq(md) {
        font-size: rem(42);
    }
}

.c-section-title.c-section-title--left {
    text-align: left;
    letter-spacing: 0.06em;
}

.c-section-title.c-section-title--right {
    text-align: right;
}

.c-section-title::before {

    @include mq(md) {
        content: attr(data-title);
        position: absolute;
        z-index: -1;
        top: 5%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: var(--color-blue-dark);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        font-size: clamp-func(100);
        line-height: calc(130 / 100);
        letter-spacing: 0.02em;
        text-transform: uppercase;
        white-space: nowrap;
    }
}

.c-section-title.c-section-title--left::before {
    left: 0;
    transform: translate(0, -50%);
    color: var(--color-white);
    opacity: 0.3;
    letter-spacing: -0.04em;
}

.c-section-title.c-section-title--right::before {
    right: 0;
    left: initial;
    transform: translate(0, -50%);
}

.c-section-title.c-section-title--dark {
    color: var(--color-dark-gray);
    text-align: left;
    font-size: rem(18);
    line-height: calc(30 / 18);
    letter-spacing: 0.06em;

    @include mq(md) {
        font-size: clamp(1.625rem, 0.994rem + 0.92vw, 1.875rem);
        line-height: calc(48 / 30);
        letter-spacing: 0.05em;
    }
}

.c-section-title.c-section-title--dark::before {
    color: var(--color-off-white);
}

.c-section-title.c-section-title--black {
    color: var(--color-dark-gray);
    text-align: left;
    font-size: rem(22);
    line-height: 1.5;

    @include mq("md") {
        font-size: rem(42);
        line-height: 1.5;
    }
}

.c-section-title.c-section-title--naby {
    color: var(--color-dark-gray);
    @include mq("md") {
        letter-spacing: 0.06em;
    }
}