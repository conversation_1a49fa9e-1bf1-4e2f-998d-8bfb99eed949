@use "../globals/"as *;

.p-header {
    height: rem(55);
    background-color: var(--color-blue-navy);
    transition: background-color 0.4s ease;

    @include mq("md") {
        height: rem(84);
    }
}

.p-header.p-header--recruit {
    background-color: transparent;
    padding-top: rem(5);
    @include mq("md") {
      padding-top: rem(4);
    }
}

// トップページのみ透明背景
body.home .p-header {
    background-color: transparent;
}

// スクロール時は必ず背景色をつける
body.home .p-header.is-scrolled,
.p-header.is-scrolled {
    background-color: var(--color-blue-navy);
}

// ドロワーメニューが開いている時は必ず背景色をつける
body.home .p-header.is-drawer-expanded,
.p-header.is-drawer-expanded {
    background-color: var(--color-blue-navy);
}

.p-header__inner {
    padding-left: rem(15);
    height: inherit;
    display: flex;
    justify-content: space-between;

    @include mq("md") {
        padding-inline: clamp(0.625rem, -2.529rem + 4.59vw, 1.875rem);
    }
}

.p-header__inner.p-header__inner--recruit {
    padding-left: rem(11);
    @include mq("md") {
        padding-left: rem(68);
    }
}

.p-header__logo {
    position: relative;
    z-index: 1000;
    max-width: rem(238);
    width: 100%;
    height: inherit;
    margin-block: 0;

    @include mq("md") {
        max-width: initial;
        width: clamp(18.75rem, 7.712rem + 16.06vw, 23.125rem);
    }
}

.p-header__logo a {
    height: inherit;
    display: flex;
    align-items: center;
}

.p-header__logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.p-header__nav {
    position: relative;
    display: none;
    height: inherit;

    @include mq("md") {
        display: block;
    }
}

.p-header__nav-list {
    display: flex;
    align-items: center;
    height: inherit;
}

.p-header__nav-item {}

.p-header__nav-item.p-header__nav-item--contact {
    margin-left: clamp(0.313rem, -2.053rem + 3.44vw, 1.25rem);
}

.p-header__nav-item.p-header__nav-item--contact .c-contact-button {
    min-width: rem(165);
}

.p-header__nav-contact-icon:before {
    content: "";
    font-size: rem(20);
}

.p-header__nav-item>a {
    padding: rem(15) clamp(0.625rem, -0.952rem + 2.29vw, 1.25rem);
    display: flex;
    align-items: center;
    font-size: clamp(0.875rem, 0.56rem + 0.46vw, 1rem);
    font-weight: var(--bold);
    line-height: 1;
    color: var(--color-white);
    letter-spacing: 0.04em;
}

.p-header__hamburger {
    margin: 0;
    padding: 0;
    outline: none;
    border: none;
    position: relative;
    z-index: 999;
    width: rem(64);
    height: inherit;
    cursor: pointer;
    transition: .3s;

    @include mq("md") {
        display: none;
    }
}

.p-header__hamburger-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: rem(36);
    height: auto;
    transition: opacity .3s ease;
}

.p-header__hamburger-icon--close {
    opacity: 1;
}

.p-header__hamburger-icon--open {
    opacity: 0;
}

.p-header__hamburger.is-open .p-header__hamburger-icon--close {
    opacity: 0;
}

.p-header__hamburger.is-open .p-header__hamburger-icon--open {
    opacity: 1;
}

.p-header__drawer {
    margin-top: rem(54);
    padding-bottom: rem(110);
    display: none;
    position: absolute;
    z-index: 900;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--color-blue-navy);
    overflow-y: scroll;
    scrollbar-width: none;
}

.p-header__drawer::-webkit-scrollbar {
    display: none;
}

.p-header__drawer-nav {
    padding-inline: var(--padding-sp);
    padding-block: rem(10) rem(36);
    width: min(100%, rem(500));
    margin-inline: auto;
}

.p-header__drawer-item {
    border-bottom: 1px solid var(--color-white);
}


.p-header__drawer-item.p-header__drawer-item--contact {
    margin-top: rem(17);
    margin-inline: auto;
    border-bottom: none;
    max-width: rem(231);
    width: 100%;
}

.p-header__drawer-link {
    padding-block: rem(23.5);
    display: block;
    font-size: rem(18);
    font-weight: var(--bold);
    color: var(--color-white);
    line-height: 1;
    letter-spacing: 0.06em;
    transition: opacity 0.3s ease;
}

@media (any-hover: hover) {
    .p-header__drawer-link:hover {
        opacity: 0.7;
    }
}

.p-header__drawer-sub-list {
    padding-bottom: rem(10);
    display: grid;
    gap: rem(10);
}

.p-header__drawer-sub-link {
    display: block;
    font-size: rem(16);
    color: var(--color-white);
    font-weight: var(--medium);
    line-height: 1;
    letter-spacing: 0.04em;
    padding-block: rem(2);
    transition: opacity 0.3s ease;
}

@media (any-hover: hover) {

    .p-header__drawer-sub-link:hover,
    .p-header__drawer-sub-link-item:hover {
        opacity: 0.7;
    }
}

.p-header__drawer-item--footer-links {
    margin-top: rem(12);
    border-bottom: none;
}


.p-header__drawer-item--footer-links .p-header__drawer-link {
    font-size: rem(12);
    font-weight: var(--medium);
    color: var(--color-white);
    line-height: calc(20 / 14);
    letter-spacing: 0.04em;
    padding-block: rem(10);
}


.p-header__drawer-contact-icon:before {
    content: "";
    font-size: rem(20);
}

.p-header__drawer-accordion-title {
    position: relative;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.p-header__drawer-accordion-title:hover {
    opacity: 1;
}

.p-header__drawer-accordion-title::before,
.p-header__drawer-accordion-title::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 0;
    width: rem(20);
    height: rem(2);
    display: block;
    background: var(--color-white);
    transition: transform .3s ease;
}

.p-header__drawer-accordion-title::before {
    transform: translateY(-50%);
}

.p-header__drawer-accordion-title::after {
    transform: translateY(-50%) rotate(90deg);
}

.p-header__drawer-accordion-title.is-open::after {
    transform: translateY(-50%);
}

.p-header__drawer-accordion-list {
    margin-top: rem(-4);
    padding-bottom: rem(15);
    display: none;
}

.p-header__drawer-accordion-list-sub {
    margin-top: rem(5);
    padding-left: rem(14);
}


.p-header__drawer-sub-link-item {
    padding: rem(7) 0;
    display: block;
    font-size: rem(16);
    color: var(--color-white);
    font-weight: var(--medium);
    line-height: 1;
    letter-spacing: 0.04em;
    transition: opacity 0.3s ease;
}