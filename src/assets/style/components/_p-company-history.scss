@use "../globals/"as *;

.p-company-history {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
    padding-block: rem(80) rem(111);

    @include mq("md") {
        padding-block: rem(162) rem(100);
    }
}

.p-company-history__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.p-company-history__bg::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    background-image: image-set(url('/images/bg_company.webp') type('image/webp'),
            url('/images/bg_company.jpg') type('image/jpg'));
    background-size: cover;
    background-position: center;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.p-company-history__inner.l-inner {
    @include mq("md") {
        max-width: rem(950);
        margin-inline: auto;
    }
}

.p-company-history__container {
    @include mq("md") {
        position: relative;
        z-index: 0;
        display: grid;
        grid-template-columns: 180fr 650fr;
        align-items: flex-start;
        gap: rem(70);
    }
}

.p-company-history__container::after {
    @include mq("md") {
        content: "history";
        position: absolute;
        top: rem(-47);
        left: calc(50% - min(48vw, rem(690)));
        color: var(--color-blue-dark);
        font-family: var(--baskervville);
        font-weight: var(--regular);
        text-transform: uppercase;
        font-size: rem(136);
        line-height: calc(128 / 136);
        letter-spacing: 0.01em;
        z-index: -1;
        text-align: right;
        white-space: pre-line;
    }
}

.p-company-history__title-wrap.c-section-title-wrap {
    @include mq("md") {
        padding-bottom: rem(40);
    }
}

.p-company-history__content {
    margin-top: rem(29);

    @include mq("md") {
        margin-top: rem(-7);
    }
}

.p-company-history__list {
    display: flex;
    flex-direction: column;
    gap: rem(22);

    @include mq("md") {
        gap: 0;
    }
}

.p-company-history__item {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    gap: rem(10);
    padding-bottom: rem(20);
    border-bottom: 1px solid var(--color-gray-medium-2);

    @include mq("md") {
        position: relative;
        flex-direction: row;
        gap: rem(44);
        padding-block: rem(24.7);
    }
}

.p-company-history__item.p-company-history__item--center {
    @include mq("md") {
        align-items: center;
    }
}

.p-company-history__term {
    display: flex;
    align-items: flex-end;

    @include mq("md") {
        flex-shrink: 0;
        min-width: rem(220);
    }
}

.p-company-history__item::after {
    @include mq("md") {
        content: "";
        position: absolute;
        bottom: rem(-2);
        left: 0;
        width: rem(225);
        height: rem(4);
        background-color: var(--color-white);
    }
}

.p-company-history__year-number {
    font-size: rem(40);
    font-weight: var(--regular);
    line-height: 1;
    font-family: var(--noto-serif-jp);
    color: var(--color-white);
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(48);
    }
}


.p-company-history__year-unit,
.p-company-history__year-era {
    margin-bottom: rem(-3);

    @include mq("md") {
        margin-bottom: initial;
    }
}


.p-company-history__year-unit {
    font-size: rem(16);
    font-weight: var(--bold);
    line-height: calc(30 / 16);
    color: var(--color-white);
    letter-spacing: 0.04em;
    margin-left: rem(2);
    align-self: flex-end;

    @include mq("md") {
        font-size: rem(18);
        margin-left: rem(4);
        margin-bottom: rem(-4);
    }
}

.p-company-history__year-era {
    font-size: rem(16);
    font-weight: var(--bold);
    line-height: calc(30 / 16);
    color: var(--color-white);
    letter-spacing: 0.04em;
    margin-left: rem(12);

    @include mq("md") {
        font-size: rem(18);
        line-height: calc(30 / 18);
    }
}


.p-company-history__description,
.p-company-history__description span {
    display: grid;
    gap: rem(10);
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(24 / 16);
    color: var(--color-white);
    letter-spacing: 0.04em;

    @include mq("md") {
        font-size: rem(18);
        line-height: calc(30 / 18);
        gap: rem(15);
    }
}