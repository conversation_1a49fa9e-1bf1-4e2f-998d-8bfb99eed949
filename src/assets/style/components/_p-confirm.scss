@use "../globals/"as *;

.p-confirm {
    padding-block: rem(60) rem(82);

    @include mq("md") {
        padding-block: rem(99) rem(100);
    }
}

.p-confirm__inner.l-inner {
    @include mq("md") {
        max-width: rem(950);
        margin-inline: auto;
    }
}


.p-confirm__text {
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;
    color: var(--color-dark-gray);
    text-align: center;

    @include mq("md") {
        font-size: rem(14);
    }
}

.p-confirm__content {
    margin-top: rem(25);
    background-color: var(--color-off-white);
    padding: rem(30) rem(20);

    @include mq("md") {
        margin-top: rem(37);
        padding: rem(60) rem(60);
    }
}

.p-confirm__form {
    display: flex;
    flex-direction: column;
    gap: rem(22);

    @include mq("md") {
        gap: rem(61);
    }
}

.p-confirm__field {
    display: flex;
    flex-direction: column;
    gap: rem(10);
    padding-bottom: rem(22);
    border-bottom: 1px solid var(--color-gray-medium-2);

    @include mq("md") {
        flex-direction: row;
        gap: rem(12);
        padding-bottom: rem(56);
    }
}


.p-confirm__zip-container {
    display: flex;
    flex-direction: column;
    gap: rem(10);

    @include mq("md") {
        flex-direction: row;
        gap: rem(12);
    }
}

.p-confirm__field.p-confirm__field--textarea {
    padding-bottom: rem(20);

    @include mq("md") {
        align-items: flex-start;
        padding-bottom: rem(55);
    }
}



.p-confirm__label {
    display: flex;
    align-items: center;
    gap: rem(10);
    pointer-events: none;

    @include mq("md") {
        min-width: rem(180);
        gap: rem(10);
    }
}


.p-confirm__label-text {
    font-size: rem(16);
    font-weight: var(--bold);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;
    color: var(--color-dark-gray);
}

.p-confirm__data {
    font-size: rem(16);
    font-weight: var(--medium);
    line-height: calc(30 / 16);
    letter-spacing: 0.04em;
    color: var(--color-dark-gray);
}

.p-confirm__message {
    margin-top: rem(30);
    text-align: center;
    font-size: rem(14);
    font-weight: var(--medium);
    line-height: calc(20 / 14);
    letter-spacing: 0.04em;
    color: var(--color-dark-gray);

    @include mq("md") {
        font-size: rem(16);
    }
}

.p-confirm__required {
    padding: 0 rem(5);
    font-size: rem(12);
    font-weight: var(--medium);
    line-height: calc(20 / 12);
    color: var(--color-white);
    background-color: var(--color-orange-dark);
}

.p-confirm__optional {
    padding: 0 rem(5);
    font-size: rem(12);
    font-weight: var(--medium);
    line-height: calc(20 / 12);
    color: var(--color-white);
    background-color: var(--color-gray-dark);
}


.p-confirm__submit-wrap {
    margin-top: rem(31);
    text-align: center;
    margin-inline: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: rem(15);

    @include mq("md") {
        margin-top: rem(40);
        flex-direction: row;
        justify-content: center;
        gap: rem(20);
    }
}

.p-confirm__return,
.p-form__send {
    width: 100%;
    max-width: rem(240);

    @include mq("md") {
        max-width: rem(270);
    }
}

.p-confirm__submit,
.p-confirm__back {
    width: 100%;
    padding: rem(17) rem(20);
    font-size: rem(16);
    font-weight: var(--semibold);
    line-height: 1.2;
    color: var(--color-white);
    background-color: var(--color-black);
    letter-spacing: 0.04em;
    border-radius: rem(32);
    transition: border-radius 0.3s ease, background-color 0.3s ease, color 0.3s ease;

    @include mq("md") {
        padding: rem(15) rem(20);
        font-size: rem(18);
        line-height: calc(34 / 18);
    }
}

@media (any-hover: hover) {
    .p-confirm__submit:hover,
    .p-confirm__back:hover {
        opacity: 1;
        border-radius: 0;
        background-color: var(--color-orange);
    }
}

//テスト環境のみの対応
.p-confirm__back {
    display: block !important;
}