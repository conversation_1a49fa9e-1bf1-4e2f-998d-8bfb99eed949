@use "sass:math";

@property --root-font-size {
    syntax: "<length>";
    inherits: false;
    initial-value: 16px;
}

:root {
    /* inner */
    --inner: 1100px;
    --padding-pc: 25px;
    --padding-sp: 30px;

    /* color */
    /* 白・グレー系 */
    --color-white: #fff;
    --color-off-white: #F8F8F8;
    --color-gray-light: #BCB0BF;
    --color-gray-medium: #9A9A9A;
    --color-gray-medium-2: #D5D5D5;
    --color-gray-dark: #707070;
    --color-gray-darker: #575757;
    --color-gray-darker-2: #BCBDBF;
    --color-dark-gray: #2B2B2B;
    --color-black: #0D0505;
    --color-black-2: #333333;

    /* 赤系 */
    --color-red-bright: #D90101;
    --color-red-dark: #ED0000;

    /* オレンジ系 */
    --color-orange: #ED6800;
    --color-orange-dark: #ED5B00;

    /* 黄色系 */
    --color-yellow: #FFD833;
    --color-yellow-light: #FCF9EC;

    /* 緑系 */
    --color-green-bright: #08C900;
    --color-green-dark: #00B73B;
    --color-green-light: #A9CFBA;
    --color-green-pale: #ECFDEB;
    --color-teal: #18693C;

    /* 青系 */
    --color-blue: #1869C7;
    --color-blue-dark: #373A48;
    --color-blue-navy: #000026;

    /* font-weight */
    --light: 300;
    --regular: 400;
    --medium: 500;
    --semibold: 600;
    --bold: 700;

    /* font-family */
    --noto-sans-jp: 'Noto Sans JP', sans-serif;
    --baskervville: 'Baskervville', serif;
    --noto-serif-jp: 'Noto Serif JP', serif;
    --jost: 'Jost', sans-serif;


    /* 8pxを余白の基準とした余白ルール */
    /* 参考：https://www.tak-dcxi.com/article/use-line-height-trim-as-css-variable/ */
    --leading-trim: calc((1em - 1lh) / 2);
    --spacing-unit: 0.5rem; //8px

    --spacing-xs: calc(var(--spacing-unit) / 2); //4px
    --spacing-sm: var(--spacing-unit); //8px
    --spacing-md: calc(var(--spacing-unit) * 2); //16px
    --spacing-lg: calc(var(--spacing-unit) * 3); //24px
    --spacing-lg-2: calc(var(--spacing-unit) * 4); //32px
    --spacing-xl: calc(var(--spacing-unit) * 5); //40px
    --spacing-xl-2: calc(var(--spacing-unit) * 6); //48px
    --spacing-2xl: calc(var(--spacing-unit) * 8); //64px
    --spacing-3xl: calc(var(--spacing-unit) * 13); //124px

    /* line-height分の余白を打ち消す */
    --spacing-xs-trim: calc(var(--spacing-xs) + var(--leading-trim));
    --spacing-sm-trim: calc(var(--spacing-sm) + var(--leading-trim));
    --spacing-md-trim: calc(var(--spacing-md) + var(--leading-trim));
    --spacing-lg-trim: calc(var(--spacing-lg) + var(--leading-trim));
    --spacing-xl-trim: calc(var(--spacing-xl) + var(--leading-trim));
    --spacing-2xl-trim: calc(var(--spacing-2xl) + var(--leading-trim));
    --spacing-3xl-trim: calc(var(--spacing-3xl) + var(--leading-trim));

    /* --leading-trimの使い方 */
    // h2 {
    //   /* デザインカンプで取得した余白が32pxの場合 */
    //   margin-block-start: calc(32px + var(--leading-trim));
    // }

    // 16pxを基準として、1pxあたりのremを計算
    --to-rem: calc(tan(atan2(1px, var(--root-font-size))) * 1rem);
    // how to use
    // font-size: calc(16 * var(--to-rem));
}

@supports not (top: 1lh) {
    :root {
        --leading-trim: 0px;
        /* `px`などの単位が必要 */
    }
}