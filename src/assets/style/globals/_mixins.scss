@use "sass:math";
@use "function"as *;

/// 背景装飾文字用のmixin
/// 必須パラメータは$contentのみ、他は全てオプション
/// @param {string} $content - 表示するテキスト（改行は \A を使用）
/// @param {string} $color [var(--color-off-white)] - 文字色
/// @param {number} $font-size [rem(100)] - フォントサイズ
/// @param {string} $top [0] - top位置
/// @param {string} $right [calc(50% - min(48vw, rem(720)))] - right位置
/// @param {string} $left [auto] - left位置
/// @param {string} $bottom [auto] - bottom位置
/// @param {number} $z-index [-1] - z-index値
/// @param {string} $line-height [1] - line-height
/// @param {string} $letter-spacing [-0.03em] - letter-spacing
/// @param {string} $text-align [left] - text-align
/// @param {string} $white-space [nowrap] - white-space（改行する場合はpre-line）
/// @param {string} $transform [translateX(0)] - transform（追加の変形が必要な場合）

@mixin bg-text($content,
    $color: var(--color-off-white),
    $font-size: rem(100),
    $top: 0,
    $right: calc(50% - min(48vw, rem(720))),
    $left: auto,
    $bottom: auto,
    $z-index: -1,
    $line-height: 1,
    $letter-spacing: -0.03em,
    $text-align: left,
    $white-space: nowrap,
    $transform: null) {
    content: $content;
    position: absolute;
    top: $top;
    right: $right;
    left: $left;
    bottom: $bottom;
    color: $color;
    font-family: var(--baskervville);
    font-weight: var(--regular);
    font-size: $font-size;
    line-height: $line-height;
    text-transform: uppercase;
    letter-spacing: $letter-spacing;
    z-index: $z-index;
    text-align: $text-align;
    white-space: $white-space;

    @if $transform {
        transform: $transform;
    }
}