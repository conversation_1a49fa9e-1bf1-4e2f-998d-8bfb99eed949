# PRやissueに関するガイドライン

- PRやissueで担当者が決まっている場合はアサインをつける
- [詳細マニュアルはこちら](https://defiant-crow-3a6.notion.site/GitHub-50351fdcf5c6470fa1a03b073fbe9ec9?pvs=4)

## PR

- レビュワーには平林を設定し、Slackでメンションする
  - 修正あればコメントに対して修正内容を記載する
  - 修正完了次第再レビュー依頼
- Approve後のPRのマージは**PR作成者**が行う
- PRの差分が大きくなりそうだったら対応するテーマごとに細かく分ける

- サイドバーのDevelopmentにissueを設定して紐づける
  - PR・issueのどちらからでも可能
  - PRがマージされたら自動で該当のissueもcloseされるようになる

